<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\RequestLog;
use App\Models\LlmModel;
use App\Models\Provider;
use App\Models\User;
use App\Models\ApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Inertia\Inertia;

class AnalyticsController extends Controller
{
    /**
     * Display the analytics dashboard.
     */
    public function index(Request $request)
    {
        $period = $request->get('period', '7d'); // 7d, 30d, 90d
        $startDate = $this->getStartDate($period);

        $analytics = [
            'overview' => $this->getOverviewStats($startDate),
            'usage_by_model' => $this->getUsageByModel($startDate),
            'usage_by_provider' => $this->getUsageByProvider($startDate),
            'cost_breakdown' => $this->getCostBreakdown($startDate),
            'daily_usage' => $this->getDailyUsage($startDate),
            'top_users' => $this->getTopUsers($startDate),
            'error_rates' => $this->getErrorRates($startDate),
        ];

        return Inertia::render('Admin/Analytics/Dashboard', [
            'analytics' => $analytics,
            'period' => $period,
        ]);
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats(Carbon $startDate): array
    {
        $totalRequests = RequestLog::where('created_at', '>=', $startDate)->count();
        $totalTokens = RequestLog::where('created_at', '>=', $startDate)->sum('tokens_used');
        $totalCost = RequestLog::where('created_at', '>=', $startDate)->sum('cost');
        $successfulRequests = RequestLog::where('created_at', '>=', $startDate)
            ->successful()
            ->count();

        $successRate = $totalRequests > 0 ? ($successfulRequests / $totalRequests) * 100 : 0;

        return [
            'total_requests' => $totalRequests,
            'total_tokens' => $totalTokens,
            'total_cost' => round($totalCost, 2),
            'success_rate' => round($successRate, 1),
            'active_users' => User::whereHas('apiKeys.requestLogs', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            })->count(),
            'active_models' => LlmModel::whereHas('requestLogs', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            })->count(),
        ];
    }

    /**
     * Get usage statistics by model.
     */
    private function getUsageByModel(Carbon $startDate): array
    {
        return RequestLog::select([
                'llm_models.name',
                'llm_models.model_identifier',
                'providers.name as provider_name',
                DB::raw('COUNT(*) as request_count'),
                DB::raw('SUM(tokens_used) as total_tokens'),
                DB::raw('SUM(cost) as total_cost'),
                DB::raw('AVG(duration_ms) as avg_duration'),
            ])
            ->join('llm_models', 'request_logs.llm_model_id', '=', 'llm_models.id')
            ->join('providers', 'llm_models.provider_id', '=', 'providers.id')
            ->where('request_logs.created_at', '>=', $startDate)
            ->groupBy('llm_models.id', 'llm_models.name', 'llm_models.model_identifier', 'providers.name')
            ->orderByDesc('request_count')
            ->get()
            ->toArray();
    }

    /**
     * Get usage statistics by provider.
     */
    private function getUsageByProvider(Carbon $startDate): array
    {
        return RequestLog::select([
                'providers.name',
                'providers.type',
                DB::raw('COUNT(*) as request_count'),
                DB::raw('SUM(tokens_used) as total_tokens'),
                DB::raw('SUM(cost) as total_cost'),
                DB::raw('AVG(duration_ms) as avg_duration'),
            ])
            ->join('providers', 'request_logs.provider_id', '=', 'providers.id')
            ->where('request_logs.created_at', '>=', $startDate)
            ->groupBy('providers.id', 'providers.name', 'providers.type')
            ->orderByDesc('request_count')
            ->get()
            ->toArray();
    }

    /**
     * Get cost breakdown by different dimensions.
     */
    private function getCostBreakdown(Carbon $startDate): array
    {
        $byModel = RequestLog::select([
                'llm_models.name',
                DB::raw('SUM(cost) as total_cost'),
                DB::raw('SUM(input_tokens) as input_tokens'),
                DB::raw('SUM(output_tokens) as output_tokens'),
            ])
            ->join('llm_models', 'request_logs.llm_model_id', '=', 'llm_models.id')
            ->where('request_logs.created_at', '>=', $startDate)
            ->groupBy('llm_models.id', 'llm_models.name')
            ->orderByDesc('total_cost')
            ->limit(10)
            ->get()
            ->toArray();

        $byUser = RequestLog::select([
                'users.name',
                'users.email',
                DB::raw('SUM(cost) as total_cost'),
                DB::raw('COUNT(*) as request_count'),
            ])
            ->join('api_keys', 'request_logs.api_key_id', '=', 'api_keys.id')
            ->join('users', 'api_keys.user_id', '=', 'users.id')
            ->where('request_logs.created_at', '>=', $startDate)
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderByDesc('total_cost')
            ->limit(10)
            ->get()
            ->toArray();

        return [
            'by_model' => $byModel,
            'by_user' => $byUser,
        ];
    }

    /**
     * Get daily usage statistics.
     */
    private function getDailyUsage(Carbon $startDate): array
    {
        return RequestLog::select([
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as request_count'),
                DB::raw('SUM(tokens_used) as total_tokens'),
                DB::raw('SUM(cost) as total_cost'),
            ])
            ->where('created_at', '>=', $startDate)
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get top users by usage.
     */
    private function getTopUsers(Carbon $startDate): array
    {
        return RequestLog::select([
                'users.name',
                'users.email',
                DB::raw('COUNT(*) as request_count'),
                DB::raw('SUM(tokens_used) as total_tokens'),
                DB::raw('SUM(cost) as total_cost'),
                DB::raw('MAX(request_logs.created_at) as last_request'),
            ])
            ->join('api_keys', 'request_logs.api_key_id', '=', 'api_keys.id')
            ->join('users', 'api_keys.user_id', '=', 'users.id')
            ->where('request_logs.created_at', '>=', $startDate)
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderByDesc('request_count')
            ->limit(20)
            ->get()
            ->toArray();
    }

    /**
     * Get error rates by model and endpoint.
     */
    private function getErrorRates(Carbon $startDate): array
    {
        $byModel = RequestLog::select([
                'llm_models.name',
                DB::raw('COUNT(*) as total_requests'),
                DB::raw('SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as error_count'),
                DB::raw('ROUND((SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as error_rate'),
            ])
            ->join('llm_models', 'request_logs.llm_model_id', '=', 'llm_models.id')
            ->where('request_logs.created_at', '>=', $startDate)
            ->groupBy('llm_models.id', 'llm_models.name')
            ->havingRaw('COUNT(*) > 0')
            ->orderByDesc(DB::raw('ROUND((SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2)'))
            ->get()
            ->toArray();

        $byEndpoint = RequestLog::select([
                'endpoint',
                DB::raw('COUNT(*) as total_requests'),
                DB::raw('SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as error_count'),
                DB::raw('ROUND((SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as error_rate'),
            ])
            ->where('created_at', '>=', $startDate)
            ->groupBy('endpoint')
            ->havingRaw('COUNT(*) > 0')
            ->orderByDesc(DB::raw('ROUND((SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2)'))
            ->get()
            ->toArray();

        return [
            'by_model' => $byModel,
            'by_endpoint' => $byEndpoint,
        ];
    }

    /**
     * Get start date based on period.
     */
    private function getStartDate(string $period): Carbon
    {
        return match ($period) {
            '1d' => Carbon::now()->subDay(),
            '7d' => Carbon::now()->subWeek(),
            '30d' => Carbon::now()->subMonth(),
            '90d' => Carbon::now()->subMonths(3),
            default => Carbon::now()->subWeek(),
        };
    }

    /**
     * Export analytics data as CSV.
     */
    public function export(Request $request)
    {
        $period = $request->get('period', '7d');
        $type = $request->get('type', 'overview'); // overview, usage, costs, errors

        // Implementation would generate CSV based on type
        // For now, return a simple response
        return response()->json([
            'message' => 'Export functionality would be implemented here',
            'period' => $period,
            'type' => $type,
        ]);
    }
}
