<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ApiKeyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $apiKeys = ApiKey::with(['user', 'requestLogs' => function ($query) {
            $query->latest()->limit(5);
        }])
            ->withCount('requestLogs')
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Admin/ApiKeys/Index', [
            'apiKeys' => $apiKeys,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::orderBy('name')->get();

        return Inertia::render('Admin/ApiKeys/Create', [
            'users' => $users,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'expires_at' => 'nullable|date|after:now',
            'permissions' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Generate a secure API key
        $keyValue = 'llmr_' . Str::random(40); // llmr_ prefix for LLM Router
        
        $apiKey = ApiKey::create([
            'user_id' => $request->user_id,
            'name' => $request->name,
            'key_hash' => hash('sha256', $keyValue),
            'key_prefix' => substr($keyValue, 0, 8) . '...',
            'expires_at' => $request->expires_at,
            'permissions' => $request->permissions ?? [],
            'is_active' => true,
            'last_used_at' => null,
        ]);

        // Return the plain text key only once
        return redirect()->route('admin.api-keys.show', $apiKey)
            ->with('success', 'API key created successfully.')
            ->with('api_key_value', $keyValue); // This will be shown only once
    }

    /**
     * Display the specified resource.
     */
    public function show(ApiKey $apiKey)
    {
        $apiKey->load([
            'user',
            'requestLogs' => function ($query) {
                $query->with('llmModel.provider')
                    ->latest()
                    ->limit(50);
            }
        ]);

        // Calculate usage statistics
        $usageStats = [
            'total_requests' => $apiKey->requestLogs()->count(),
            'total_tokens' => $apiKey->requestLogs()->sum('tokens_used'),
            'total_cost' => $apiKey->requestLogs()->sum('cost'),
            'requests_last_30_days' => $apiKey->requestLogs()
                ->where('created_at', '>=', now()->subDays(30))
                ->count(),
            'avg_tokens_per_request' => $apiKey->requestLogs()->avg('tokens_used'),
            'most_used_model' => $apiKey->requestLogs()
                ->with('llmModel')
                ->get()
                ->groupBy('llm_model_id')
                ->sortByDesc(function ($group) {
                    return $group->count();
                })
                ->first()
                ->first()
                ->llmModel ?? null,
        ];

        return Inertia::render('Admin/ApiKeys/Show', [
            'apiKey' => $apiKey,
            'usageStats' => $usageStats,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ApiKey $apiKey)
    {
        $apiKey->load('user');
        $users = User::orderBy('name')->get();

        return Inertia::render('Admin/ApiKeys/Edit', [
            'apiKey' => $apiKey,
            'users' => $users,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApiKey $apiKey)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'expires_at' => 'nullable|date|after:now',
            'permissions' => 'sometimes|array',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $apiKey->update([
            'name' => $request->name,
            'expires_at' => $request->expires_at,
            'permissions' => $request->permissions ?? [],
            'is_active' => $request->is_active ?? true,
        ]);

        return redirect()->route('admin.api-keys.index')
            ->with('success', 'API key updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApiKey $apiKey)
    {
        // Soft delete to preserve request logs
        $apiKey->delete();

        return redirect()->route('admin.api-keys.index')
            ->with('success', 'API key revoked successfully.');
    }

    /**
     * Revoke an API key (set as inactive).
     */
    public function revoke(ApiKey $apiKey)
    {
        $apiKey->update([
            'is_active' => false,
            'revoked_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API key revoked successfully.',
        ]);
    }

    /**
     * Reactivate an API key.
     */
    public function activate(ApiKey $apiKey)
    {
        // Check if key is not expired
        if ($apiKey->expires_at && $apiKey->expires_at->isPast()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot activate expired API key.',
            ], 400);
        }

        $apiKey->update([
            'is_active' => true,
            'revoked_at' => null,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API key activated successfully.',
        ]);
    }

    /**
     * Get API key usage statistics.
     */
    public function usage(ApiKey $apiKey)
    {
        $usage = $apiKey->requestLogs()
            ->selectRaw('DATE(created_at) as date, COUNT(*) as requests, SUM(tokens_used) as tokens, SUM(cost) as cost')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'usage' => $usage,
        ]);
    }
}
