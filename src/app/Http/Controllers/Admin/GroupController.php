<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\LlmModel;
use App\Models\GroupModelPermission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class GroupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $groups = Group::withCount(['users', 'llmModels'])->get();

        return Inertia::render('Admin/Groups/Index', [
            'groups' => $groups,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $models = LlmModel::with('provider')->where('is_active', true)->get();

        return Inertia::render('Admin/Groups/Create', [
            'models' => $models,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:groups',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'has_all_models_access' => 'boolean',
            'permissions' => 'sometimes|array',
            'model_permissions' => 'sometimes|array',
            'model_permissions.*.model_id' => 'required|exists:llm_models,id',
            'model_permissions.*.can_access' => 'boolean',
            'model_permissions.*.restrictions' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::transaction(function () use ($request) {
            $group = Group::create([
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => $request->is_active ?? true,
                'has_all_models_access' => $request->has_all_models_access ?? false,
                'permissions' => $request->permissions ?? [],
            ]);

            // Create model permissions (only if not using "all models" access)
            if (!$group->has_all_models_access && $request->has('model_permissions')) {
                foreach ($request->model_permissions as $permission) {
                    GroupModelPermission::create([
                        'group_id' => $group->id,
                        'llm_model_id' => $permission['model_id'],
                        'can_access' => $permission['can_access'] ?? true,
                        'restrictions' => $permission['restrictions'] ?? [],
                    ]);
                }
            }
        });

        return redirect()->route('admin.groups.index')
            ->with('success', 'Group created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Group $group)
    {
        $group->load([
            'users',
            'llmModels.provider',
            'groupModelPermissions.llmModel.provider'
        ]);

        return Inertia::render('Admin/Groups/Show', [
            'group' => $group,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Group $group)
    {
        $group->load(['groupModelPermissions.llmModel.provider']);
        $models = LlmModel::with('provider')->where('is_active', true)->get();

        return Inertia::render('Admin/Groups/Edit', [
            'group' => $group,
            'models' => $models,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Group $group)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:groups,name,' . $group->id,
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'has_all_models_access' => 'boolean',
            'permissions' => 'sometimes|array',
            'model_permissions' => 'sometimes|array',
            'model_permissions.*.model_id' => 'required|exists:llm_models,id',
            'model_permissions.*.can_access' => 'boolean',
            'model_permissions.*.restrictions' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::transaction(function () use ($request, $group) {
            $group->update([
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => $request->is_active ?? true,
                'has_all_models_access' => $request->has_all_models_access ?? false,
                'permissions' => $request->permissions ?? [],
            ]);

            // Update model permissions
            // Always delete existing permissions first
            $group->groupModelPermissions()->delete();

            // Only create individual model permissions if not using "all models" access
            if (!$group->has_all_models_access && $request->has('model_permissions')) {
                foreach ($request->model_permissions as $permission) {
                    GroupModelPermission::create([
                        'group_id' => $group->id,
                        'llm_model_id' => $permission['model_id'],
                        'can_access' => $permission['can_access'] ?? true,
                        'restrictions' => $permission['restrictions'] ?? [],
                    ]);
                }
            }
        });

        return redirect()->route('admin.groups.index')
            ->with('success', 'Group updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Group $group)
    {
        // Check if group has users
        if ($group->users()->count() > 0) {
            return back()->with('error', 'Cannot delete group with assigned users.');
        }

        $group->delete();

        return redirect()->route('admin.groups.index')
            ->with('success', 'Group deleted successfully.');
    }
}
