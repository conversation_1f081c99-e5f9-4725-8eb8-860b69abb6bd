<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LlmModel;
use App\Models\Provider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class LlmModelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $models = LlmModel::with(['provider', 'groupModelPermissions.group'])
            ->orderBy('provider_id')
            ->orderBy('name')
            ->get();

        return Inertia::render('Admin/Models/Index', [
            'models' => $models,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $providers = Provider::where('is_active', true)->get();

        return Inertia::render('Admin/Models/Create', [
            'providers' => $providers,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:providers,id',
            'name' => 'required|string|max:255',
            'model_identifier' => 'required|string|max:255',
            'is_active' => 'boolean',
            'max_tokens' => 'nullable|integer|min:1',
            'cost_per_input_token' => 'nullable|numeric|min:0',
            'cost_per_output_token' => 'nullable|numeric|min:0',
            'capabilities' => 'sometimes|array',
            'config' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Check for unique model identifier per provider
        $exists = LlmModel::where('provider_id', $request->provider_id)
            ->where('model_identifier', $request->model_identifier)
            ->exists();

        if ($exists) {
            return back()->withErrors([
                'model_identifier' => 'This model identifier already exists for the selected provider.'
            ])->withInput();
        }

        $model = LlmModel::create($request->all());

        return redirect()->route('admin.models.index')
            ->with('success', 'Model created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(LlmModel $model)
    {
        $model->load([
            'provider',
            'groupModelPermissions.group',
            'requestLogs' => function ($query) {
                $query->latest()->limit(20);
            }
        ]);

        // Calculate usage statistics
        $usageStats = [
            'total_requests' => $model->requestLogs()->count(),
            'total_tokens' => $model->requestLogs()->sum('tokens_used'),
            'total_cost' => $model->requestLogs()->sum('cost'),
            'avg_tokens_per_request' => $model->requestLogs()->avg('tokens_used'),
            'success_rate' => $model->requestLogs()->successful()->count() / max($model->requestLogs()->count(), 1) * 100,
        ];

        return Inertia::render('Admin/Models/Show', [
            'model' => $model,
            'usageStats' => $usageStats,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LlmModel $model)
    {
        $providers = Provider::where('is_active', true)->get();
        $model->load('provider');

        return Inertia::render('Admin/Models/Edit', [
            'model' => $model,
            'providers' => $providers,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LlmModel $model)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:providers,id',
            'name' => 'required|string|max:255',
            'model_identifier' => 'required|string|max:255',
            'is_active' => 'boolean',
            'max_tokens' => 'nullable|integer|min:1',
            'cost_per_input_token' => 'nullable|numeric|min:0',
            'cost_per_output_token' => 'nullable|numeric|min:0',
            'capabilities' => 'sometimes|array',
            'config' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Check for unique model identifier per provider (excluding current model)
        $exists = LlmModel::where('provider_id', $request->provider_id)
            ->where('model_identifier', $request->model_identifier)
            ->where('id', '!=', $model->id)
            ->exists();

        if ($exists) {
            return back()->withErrors([
                'model_identifier' => 'This model identifier already exists for the selected provider.'
            ])->withInput();
        }

        $model->update($request->all());

        return redirect()->route('admin.models.index')
            ->with('success', 'Model updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LlmModel $model)
    {
        // Check if model has request logs
        if ($model->requestLogs()->count() > 0) {
            return back()->with('error', 'Cannot delete model with existing request logs.');
        }

        $model->delete();

        return redirect()->route('admin.models.index')
            ->with('success', 'Model deleted successfully.');
    }
}
