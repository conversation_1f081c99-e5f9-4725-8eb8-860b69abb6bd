<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Provider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ProviderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $providers = Provider::with(['llmModels' => function ($query) {
            $query->where('is_active', true);
        }])->get();

        return Inertia::render('Admin/Providers/Index', [
            'providers' => $providers,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/Providers/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:providers',
            'type' => 'required|in:anthropic,openai,openai_compatible',
            'base_url' => 'required|url',
            'api_key' => 'required|string',
            'is_active' => 'boolean',
            'config' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $provider = Provider::create($request->all());

        return redirect()->route('admin.providers.index')
            ->with('success', 'Provider created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Provider $provider)
    {
        $provider->load(['llmModels', 'requestLogs' => function ($query) {
            $query->latest()->limit(10);
        }]);

        return Inertia::render('Admin/Providers/Show', [
            'provider' => $provider,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Provider $provider)
    {
        return Inertia::render('Admin/Providers/Edit', [
            'provider' => $provider,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Provider $provider)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:providers,name,' . $provider->id,
            'type' => 'required|in:anthropic,openai,openai_compatible',
            'base_url' => 'required|url',
            'api_key' => 'sometimes|string',
            'is_active' => 'boolean',
            'config' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Only update API key if provided
        if (empty($data['api_key'])) {
            unset($data['api_key']);
        }

        $provider->update($data);

        return redirect()->route('admin.providers.index')
            ->with('success', 'Provider updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Provider $provider)
    {
        // Check if provider has associated models
        if ($provider->llmModels()->count() > 0) {
            return back()->with('error', 'Cannot delete provider with associated models.');
        }

        $provider->delete();

        return redirect()->route('admin.providers.index')
            ->with('success', 'Provider deleted successfully.');
    }

    /**
     * Test provider connection.
     */
    public function testConnection(Provider $provider)
    {
        // This would implement a test API call to verify the provider is working
        // For now, just return success
        return response()->json([
            'success' => true,
            'message' => 'Provider connection test successful.',
        ]);
    }
}
