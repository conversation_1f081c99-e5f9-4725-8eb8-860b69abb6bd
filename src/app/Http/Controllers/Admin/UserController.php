<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Group;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $users = User::with(['groups', 'apiKeys'])
            ->withCount(['apiKeys'])
            ->orderBy('name')
            ->get();

        // Add request logs count manually for each user
        $users->each(function ($user) {
            try {
                $user->request_logs_count = $user->requestLogs()->count();
            } catch (\Exception) {
                $user->request_logs_count = 0;
            }
        });

        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $groups = Group::where('is_active', true)->get();

        return Inertia::render('Admin/Users/<USER>', [
            'groups' => $groups,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'groups' => 'sometimes|array',
            'groups.*' => 'exists:groups,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::transaction(function () use ($request) {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'email_verified_at' => now(), // Auto-verify for admin-created users
            ]);

            // Attach groups if provided
            if ($request->has('groups') && is_array($request->groups)) {
                $user->groups()->attach($request->groups);
            }
        });

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load([
            'groups.llmModels',
            'apiKeys' => function ($query) {
                $query->orderBy('created_at', 'desc');
            }
        ]);

        // Get recent request logs manually
        $recentLogs = [];
        try {
            $recentLogs = $user->requestLogs()
                ->with('llmModel.provider')
                ->latest()
                ->limit(20)
                ->get();
        } catch (\Exception) {
            $recentLogs = collect();
        }

        // Calculate usage statistics
        try {
            $usageStats = [
                'total_requests' => $user->requestLogs()->count(),
                'total_tokens' => $user->requestLogs()->sum('tokens_used'),
                'total_cost' => $user->requestLogs()->sum('cost'),
                'active_api_keys' => $user->apiKeys()->active()->count(),
                'accessible_models' => $user->accessibleLlmModels()->count(),
            ];
        } catch (\Exception) {
            $usageStats = [
                'total_requests' => 0,
                'total_tokens' => 0,
                'total_cost' => 0,
                'active_api_keys' => $user->apiKeys()->active()->count(),
                'accessible_models' => $user->accessibleLlmModels()->count(),
            ];
        }

        // Add recent logs to user object
        $user->request_logs = $recentLogs;

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
            'usageStats' => $usageStats,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $user->load('groups');
        $groups = Group::where('is_active', true)->get();

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
            'groups' => $groups,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'groups' => 'sometimes|array',
            'groups.*' => 'exists:groups,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::transaction(function () use ($request, $user) {
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
            ];

            // Only update password if provided
            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            }

            $user->update($userData);

            // Sync groups
            if ($request->has('groups')) {
                $user->groups()->sync($request->groups ?? []);
            }
        });

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Check if user has active API keys
        if ($user->apiKeys()->active()->count() > 0) {
            return back()->with('error', 'Cannot delete user with active API keys. Please revoke all API keys first.');
        }

        // Soft delete to preserve request logs
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status (if implementing user activation/deactivation).
     */
    public function toggleStatus(User $user)
    {
        // This could be implemented if you add an 'is_active' field to users table
        // For now, we'll use the existing soft delete functionality
        
        return response()->json([
            'success' => true,
            'message' => 'User status updated successfully.',
        ]);
    }
}
