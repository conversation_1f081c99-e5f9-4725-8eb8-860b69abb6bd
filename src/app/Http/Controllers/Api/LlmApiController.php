<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\LlmRouterService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class LlmApiController extends Controller
{
    protected LlmRouterService $routerService;

    public function __construct(LlmRouterService $routerService)
    {
        $this->routerService = $routerService;
    }

    /**
     * Handle chat completions requests.
     * POST /v1/chat/completions
     */
    public function chatCompletions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'model' => 'required|string',
            'messages' => 'required|array|min:1',
            'messages.*.role' => 'required|string|in:system,user,assistant,tool',
            'messages.*.content' => 'required|string',
            'max_tokens' => 'sometimes|integer|min:1',
            'temperature' => 'sometimes|numeric|min:0|max:2',
            'top_p' => 'sometimes|numeric|min:0|max:1',
            'stream' => 'sometimes|boolean',
            'stop' => 'sometimes|array|max:4',
            'presence_penalty' => 'sometimes|numeric|min:-2|max:2',
            'frequency_penalty' => 'sometimes|numeric|min:-2|max:2',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => [
                    'message' => 'Invalid request: ' . $validator->errors()->first(),
                    'type' => 'invalid_request_error',
                    'param' => null,
                    'code' => null,
                ]
            ], 400);
        }

        return $this->routerService->routeRequest(
            '/v1/chat/completions',
            $request->all(),
            $request->get('authenticated_user'),
            $request->get('api_key_model')
        );
    }

    /**
     * Handle text completions requests.
     * POST /v1/completions
     */
    public function completions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'model' => 'required|string',
            'prompt' => 'required|string',
            'max_tokens' => 'sometimes|integer|min:1',
            'temperature' => 'sometimes|numeric|min:0|max:2',
            'top_p' => 'sometimes|numeric|min:0|max:1',
            'stream' => 'sometimes|boolean',
            'stop' => 'sometimes|array|max:4',
            'presence_penalty' => 'sometimes|numeric|min:-2|max:2',
            'frequency_penalty' => 'sometimes|numeric|min:-2|max:2',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => [
                    'message' => 'Invalid request: ' . $validator->errors()->first(),
                    'type' => 'invalid_request_error',
                    'param' => null,
                    'code' => null,
                ]
            ], 400);
        }

        return $this->routerService->routeRequest(
            '/v1/completions',
            $request->all(),
            $request->get('authenticated_user'),
            $request->get('api_key_model')
        );
    }

    /**
     * Handle responses requests (newer OpenAI API).
     * POST /v1/responses
     */
    public function responses(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'model' => 'required|string',
            'messages' => 'sometimes|array|min:1',
            'messages.*.role' => 'required_with:messages|string|in:system,user,assistant,tool',
            'messages.*.content' => 'required_with:messages|string',
            'instructions' => 'sometimes|string',
            'max_tokens' => 'sometimes|integer|min:1',
            'temperature' => 'sometimes|numeric|min:0|max:2',
            'top_p' => 'sometimes|numeric|min:0|max:1',
            'stream' => 'sometimes|boolean',
            'response_format' => 'sometimes|array',
            'response_format.type' => 'sometimes|string|in:text,json_object,json_schema',
            'tools' => 'sometimes|array',
            'tool_choice' => 'sometimes|string|in:none,auto,required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => [
                    'message' => 'Invalid request: ' . $validator->errors()->first(),
                    'type' => 'invalid_request_error',
                    'param' => null,
                    'code' => null,
                ]
            ], 400);
        }

        return $this->routerService->routeRequest(
            '/v1/responses',
            $request->all(),
            $request->get('authenticated_user'),
            $request->get('api_key_model')
        );
    }

    /**
     * List available models for the authenticated user.
     * GET /v1/models
     */
    public function models(Request $request): JsonResponse
    {
        $user = $request->get('authenticated_user');
        $models = $this->routerService->getAvailableModels($user);

        return response()->json([
            'object' => 'list',
            'data' => $models,
        ]);
    }
}
