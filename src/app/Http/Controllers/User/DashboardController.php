<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the user dashboard.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Load user data with relationships
        $user->load([
            'groups.llmModels',
            'apiKeys' => function ($query) {
                $query->active()->orderBy('created_at', 'desc');
            }
        ]);

        // Get user statistics
        $stats = [
            'total_api_keys' => $user->apiKeys()->count(),
            'active_api_keys' => $user->apiKeys()->active()->count(),
            'total_groups' => $user->groups()->count(),
            'accessible_models' => $user->accessibleLlmModels()->count(),
        ];

        // Get recent API key usage (if request logs exist)
        $recentUsage = [];
        try {
            if (class_exists('App\Models\RequestLog')) {
                $recentUsage = $user->requestLogs()
                    ->with('llmModel.provider')
                    ->latest()
                    ->limit(10)
                    ->get();
            }
        } catch (\Exception) {
            // RequestLog table might not exist or have the right structure
            $recentUsage = [];
        }

        return Inertia::render('User/Dashboard', [
            'user' => $user,
            'stats' => $stats,
            'recentUsage' => $recentUsage,
        ]);
    }

    /**
     * Display the user's accessible models grouped by their groups.
     */
    public function models(Request $request)
    {
        $user = $request->user();

        // Load user with groups and their accessible models
        $user->load([
            'groups' => function ($query) {
                $query->where('is_active', true);
            },
            'groups.llmModels' => function ($query) {
                $query->where('is_active', true)
                      ->with('provider')
                      ->orderBy('name');
            }
        ]);

        // Get all accessible models for the user
        $accessibleModels = $user->accessibleLlmModels()
            ->with('provider')
            ->orderBy('name')
            ->get();

        return Inertia::render('User/Models', [
            'user' => $user,
            'accessibleModels' => $accessibleModels,
        ]);
    }
}
