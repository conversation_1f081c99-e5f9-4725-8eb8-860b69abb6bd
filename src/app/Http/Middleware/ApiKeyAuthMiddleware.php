<?php

namespace App\Http\Middleware;

use App\Models\ApiKey;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ApiKeyAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Extract API key from Authorization header
        $authHeader = $request->header('Authorization');

        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            return $this->unauthorizedResponse('Missing or invalid Authorization header');
        }

        $apiKey = substr($authHeader, 7); // Remove "Bearer " prefix

        if (empty($apiKey)) {
            return $this->unauthorizedResponse('API key is required');
        }

        // Find and validate the API key
        $apiKeyModel = ApiKey::findByKey($apiKey);

        if (!$apiKeyModel) {
            return $this->unauthorizedResponse('Invalid API key');
        }

        if (!$apiKeyModel->isValid()) {
            return $this->unauthorizedResponse('API key is inactive or expired');
        }

        // Load the user and their groups
        $user = $apiKeyModel->user()->with('groups.llmModels')->first();

        if (!$user) {
            return $this->unauthorizedResponse('User not found');
        }

        // Add user and API key to request for use in controllers
        $request->merge([
            'authenticated_user' => $user,
            'api_key_model' => $apiKeyModel,
        ]);

        // Update last used timestamp (async to avoid blocking)
        dispatch(function () use ($apiKeyModel) {
            $apiKeyModel->markAsUsed();
        })->afterResponse();

        return $next($request);
    }

    /**
     * Return an unauthorized response in OpenAI API format.
     */
    private function unauthorizedResponse(string $message): JsonResponse
    {
        return response()->json([
            'error' => [
                'message' => $message,
                'type' => 'invalid_request_error',
                'param' => null,
                'code' => 'invalid_api_key',
            ]
        ], 401);
    }
}
