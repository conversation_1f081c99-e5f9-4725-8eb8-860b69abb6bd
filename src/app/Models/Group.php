<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Group extends Model
{
    use HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'is_active',
        'has_all_models_access',
        'permissions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'has_all_models_access' => 'boolean',
            'permissions' => 'array',
        ];
    }

    /**
     * Get the users that belong to this group.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_groups')
            ->withTimestamps();
    }

    /**
     * Get the LLM models that this group has access to.
     */
    public function llmModels(): BelongsToMany
    {
        return $this->belongsToMany(LlmModel::class, 'group_model_permissions')
            ->withPivot(['can_access', 'restrictions'])
            ->withTimestamps();
    }

    /**
     * Get the group model permissions for this group.
     */
    public function groupModelPermissions(): HasMany
    {
        return $this->hasMany(GroupModelPermission::class);
    }

    /**
     * Get the user groups for this group.
     */
    public function userGroups(): HasMany
    {
        return $this->hasMany(UserGroup::class);
    }

    /**
     * Scope a query to only include active groups.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if the group has access to a specific model.
     */
    public function hasAccessToModel(LlmModel $model): bool
    {
        if ($this->has_all_models_access && $model->is_active) {
            return true;
        }

        return $this->llmModels()
            ->where('llm_model_id', $model->id)
            ->wherePivot('can_access', true)
            ->exists();
    }

    /**
     * Get all accessible models for this group.
     */
    public function accessibleModels()
    {
        if ($this->has_all_models_access) {
            return LlmModel::where('is_active', true);
        }

        return $this->llmModels()
            ->wherePivot('can_access', true)
            ->where('is_active', true);
    }
}
