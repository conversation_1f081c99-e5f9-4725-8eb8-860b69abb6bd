<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GroupModelPermission extends Model
{
    use HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'group_id',
        'llm_model_id',
        'can_access',
        'restrictions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'can_access' => 'boolean',
            'restrictions' => 'array',
        ];
    }

    /**
     * Get the group that owns this permission.
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    /**
     * Get the LLM model that this permission applies to.
     */
    public function llmModel(): BelongsTo
    {
        return $this->belongsTo(LlmModel::class);
    }

    /**
     * Scope a query to only include permissions that allow access.
     */
    public function scopeAllowAccess($query)
    {
        return $query->where('can_access', true);
    }

    /**
     * Scope a query to only include permissions that deny access.
     */
    public function scopeDenyAccess($query)
    {
        return $query->where('can_access', false);
    }

    /**
     * Check if the permission allows access.
     */
    public function allowsAccess(): bool
    {
        return $this->can_access;
    }

    /**
     * Get a specific restriction value.
     */
    public function getRestriction(string $key, $default = null)
    {
        return data_get($this->restrictions, $key, $default);
    }

    /**
     * Set a specific restriction value.
     */
    public function setRestriction(string $key, $value): void
    {
        $restrictions = $this->restrictions ?? [];
        data_set($restrictions, $key, $value);
        $this->restrictions = $restrictions;
    }
}
