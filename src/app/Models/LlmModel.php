<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class LlmModel extends Model
{
    use HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'provider_id',
        'name',
        'model_identifier',
        'is_active',
        'max_tokens',
        'cost_per_input_token',
        'cost_per_output_token',
        'capabilities',
        'config',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'max_tokens' => 'integer',
            'cost_per_input_token' => 'decimal:8',
            'cost_per_output_token' => 'decimal:8',
            'capabilities' => 'array',
            'config' => 'array',
        ];
    }

    /**
     * Get the provider that owns this model.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the groups that have access to this model.
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'group_model_permissions')
            ->withPivot(['can_access', 'restrictions'])
            ->withTimestamps();
    }

    /**
     * Get the group model permissions for this model.
     */
    public function groupModelPermissions(): HasMany
    {
        return $this->hasMany(GroupModelPermission::class);
    }

    /**
     * Get the request logs for this model.
     */
    public function requestLogs(): HasMany
    {
        return $this->hasMany(RequestLog::class);
    }

    /**
     * Scope a query to only include active models.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by capability.
     */
    public function scopeWithCapability($query, string $capability)
    {
        return $query->whereJsonContains('capabilities', $capability);
    }

    /**
     * Check if the model supports a specific capability.
     */
    public function hasCapability(string $capability): bool
    {
        return in_array($capability, $this->capabilities ?? []);
    }

    /**
     * Calculate the cost for a given number of tokens.
     */
    public function calculateCost(int $inputTokens, int $outputTokens): float
    {
        $inputCost = ($this->cost_per_input_token ?? 0) * ($inputTokens / 1000);
        $outputCost = ($this->cost_per_output_token ?? 0) * ($outputTokens / 1000);

        return $inputCost + $outputCost;
    }
}
