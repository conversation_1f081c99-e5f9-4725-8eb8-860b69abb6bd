<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Provider extends Model
{
    use HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'base_url',
        'api_key',
        'is_active',
        'config',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'config' => 'array',
            'api_key' => 'encrypted',
        ];
    }

    /**
     * Get the LLM models for this provider.
     */
    public function llmModels(): HasMany
    {
        return $this->hasMany(LlmModel::class);
    }

    /**
     * Get the active LLM models for this provider.
     */
    public function activeLlmModels(): HasMany
    {
        return $this->hasMany(LlmModel::class)->where('is_active', true);
    }

    /**
     * Get the request logs for this provider.
     */
    public function requestLogs(): Has<PERSON>any
    {
        return $this->hasMany(RequestLog::class);
    }

    /**
     * Scope a query to only include active providers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by provider type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }
}
