<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RequestLog extends Model
{
    use HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'api_key_id',
        'provider_id',
        'llm_model_id',
        'endpoint',
        'method',
        'request_data',
        'response_data',
        'tokens_used',
        'input_tokens',
        'output_tokens',
        'cost',
        'duration_ms',
        'status_code',
        'error_message',
        'request_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'request_data' => 'array',
            'response_data' => 'array',
            'tokens_used' => 'integer',
            'input_tokens' => 'integer',
            'output_tokens' => 'integer',
            'cost' => 'decimal:6',
            'duration_ms' => 'integer',
            'status_code' => 'integer',
        ];
    }

    /**
     * Get the API key that made this request.
     */
    public function apiKey(): BelongsTo
    {
        return $this->belongsTo(ApiKey::class);
    }

    /**
     * Get the provider that handled this request.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the LLM model used for this request.
     */
    public function llmModel(): BelongsTo
    {
        return $this->belongsTo(LlmModel::class);
    }

    /**
     * Get the user who made this request through the API key.
     */
    public function user(): BelongsTo
    {
        return $this->apiKey()->getRelated()->user();
    }

    /**
     * Scope a query to only include successful requests.
     */
    public function scopeSuccessful($query)
    {
        return $query->whereBetween('status_code', [200, 299]);
    }

    /**
     * Scope a query to only include failed requests.
     */
    public function scopeFailed($query)
    {
        return $query->where('status_code', '>=', 400);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope a query to filter by API key.
     */
    public function scopeForApiKey($query, $apiKeyId)
    {
        return $query->where('api_key_id', $apiKeyId);
    }

    /**
     * Scope a query to filter by provider.
     */
    public function scopeForProvider($query, $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Scope a query to filter by model.
     */
    public function scopeForModel($query, $modelId)
    {
        return $query->where('llm_model_id', $modelId);
    }

    /**
     * Check if the request was successful.
     */
    public function wasSuccessful(): bool
    {
        return $this->status_code >= 200 && $this->status_code < 300;
    }

    /**
     * Get the total cost for a collection of request logs.
     */
    public static function getTotalCost($query)
    {
        return $query->sum('cost');
    }

    /**
     * Get the total tokens used for a collection of request logs.
     */
    public static function getTotalTokens($query)
    {
        return $query->sum('tokens_used');
    }
}
