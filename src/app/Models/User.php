<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the groups that this user belongs to.
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'user_groups')
            ->withTimestamps();
    }

    /**
     * Get the API keys for this user.
     */
    public function apiKeys(): HasMany
    {
        return $this->hasMany(ApiKey::class);
    }

    /**
     * Get the active API keys for this user.
     */
    public function activeApiKeys(): HasMany
    {
        return $this->hasMany(ApiKey::class)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            });
    }

    /**
     * Get the user groups for this user.
     */
    public function userGroups(): HasMany
    {
        return $this->hasMany(UserGroup::class);
    }

    /**
     * Get the request logs for this user through their API keys.
     */
    public function requestLogs()
    {
        return RequestLog::whereHas('apiKey', function ($query) {
            $query->where('user_id', $this->id);
        });
    }

    /**
     * Get all accessible LLM models for this user based on group memberships.
     */
    public function accessibleLlmModels()
    {
        $groupIds = $this->groups()->pluck('groups.id');

        return LlmModel::whereHas('groupModelPermissions', function ($query) use ($groupIds) {
            $query->whereIn('group_id', $groupIds)
                  ->where('can_access', true);
        })->where('is_active', true);
    }

    /**
     * Check if the user has access to a specific LLM model.
     */
    public function hasAccessToModel(LlmModel $model): bool
    {
        return $this->groups()
            ->whereHas('llmModels', function ($query) use ($model) {
                $query->where('llm_model_id', $model->id)
                      ->where('can_access', true);
            })
            ->exists();
    }

    /**
     * Check if the user is in a specific group.
     */
    public function isInGroup(Group $group): bool
    {
        return $this->groups()->where('group_id', $group->id)->exists();
    }

    /**
     * Check if the user has admin privileges.
     */
    public function isAdmin(): bool
    {
        return $this->groups()
            ->where('is_active', true)
            ->whereJsonContains('permissions->admin_access', true)
            ->exists();
    }
}
