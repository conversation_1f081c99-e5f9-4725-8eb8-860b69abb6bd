<?php

namespace App\Services;

use App\Models\LlmModel;
use App\Models\Provider;
use App\Models\User;
use App\Models\RequestLog;
use App\Models\ApiKey;
use App\Services\Providers\AnthropicAdapter;
use App\Services\Providers\OpenAiAdapter;
use App\Services\Providers\OpenAiCompatibleAdapter;
use App\Services\TokenizationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Exception;

class LlmRouterService
{
    protected TokenizationService $tokenizationService;

    public function __construct(TokenizationService $tokenizationService)
    {
        $this->tokenizationService = $tokenizationService;
    }

    /**
     * Route a request to the appropriate LLM provider.
     */
    public function routeRequest(
        string $endpoint,
        array $requestData,
        User $user,
        ApiKey $apiKey,
        string $method = 'POST'
    ): JsonResponse {
        $startTime = microtime(true);
        
        try {
            // Extract model from request
            $modelIdentifier = $requestData['model'] ?? null;
            
            if (!$modelIdentifier) {
                return $this->errorResponse('Model is required', 400);
            }

            // Find the LLM model
            $llmModel = LlmModel::where('model_identifier', $modelIdentifier)
                ->where('is_active', true)
                ->with('provider')
                ->first();

            if (!$llmModel) {
                return $this->errorResponse("Model '{$modelIdentifier}' not found", 404);
            }

            // Check if user has access to this model
            if (!$user->hasAccessToModel($llmModel)) {
                return $this->errorResponse("Access denied to model '{$modelIdentifier}'", 403);
            }

            // Get the provider adapter
            $adapter = $this->getProviderAdapter($llmModel->provider);
            
            if (!$adapter) {
                return $this->errorResponse('Provider adapter not available', 500);
            }

            // Route the request to the provider
            $response = $adapter->makeRequest($endpoint, $requestData, $llmModel);
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000); // Convert to milliseconds

            // Log the request
            $this->logRequest(
                $apiKey,
                $llmModel,
                $endpoint,
                $method,
                $requestData,
                $response,
                $duration
            );

            return response()->json($response['body'], $response['status']);

        } catch (Exception $e) {
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000);

            Log::error('LLM Router Error', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'model' => $modelIdentifier ?? 'unknown',
                'endpoint' => $endpoint,
            ]);

            // Log the failed request
            if (isset($llmModel)) {
                $this->logRequest(
                    $apiKey,
                    $llmModel,
                    $endpoint,
                    $method,
                    $requestData,
                    ['error' => $e->getMessage()],
                    $duration,
                    500
                );
            }

            return $this->errorResponse('Internal server error', 500);
        }
    }

    /**
     * Get the appropriate provider adapter.
     */
    private function getProviderAdapter(Provider $provider)
    {
        return match ($provider->type) {
            'anthropic' => new AnthropicAdapter($provider),
            'openai' => new OpenAiAdapter($provider),
            'openai_compatible' => new OpenAiCompatibleAdapter($provider),
            default => null,
        };
    }

    /**
     * Log the request to the database.
     */
    private function logRequest(
        ApiKey $apiKey,
        LlmModel $llmModel,
        string $endpoint,
        string $method,
        array $requestData,
        array $responseData,
        int $duration,
        int $statusCode = 200
    ): void {
        try {
            // Extract token usage from response or calculate if not provided
            $inputTokens = $responseData['usage']['prompt_tokens'] ?? null;
            $outputTokens = $responseData['usage']['completion_tokens'] ?? null;
            $totalTokens = $responseData['usage']['total_tokens'] ?? null;

            // If tokens not provided by the API, calculate them ourselves
            if (!$inputTokens || !$outputTokens) {
                $calculatedTokens = $this->calculateTokenUsage($requestData, $responseData, $llmModel, $endpoint);
                $inputTokens = $inputTokens ?? $calculatedTokens['input_tokens'];
                $outputTokens = $outputTokens ?? $calculatedTokens['output_tokens'];
                $totalTokens = $totalTokens ?? ($inputTokens + $outputTokens);
            }

            $cost = null;
            if ($inputTokens && $outputTokens) {
                $cost = $llmModel->calculateCost($inputTokens, $outputTokens);
            }

            RequestLog::create([
                'api_key_id' => $apiKey->id,
                'provider_id' => $llmModel->provider_id,
                'llm_model_id' => $llmModel->id,
                'endpoint' => $endpoint,
                'method' => $method,
                'request_data' => $requestData,
                'response_data' => $responseData,
                'tokens_used' => $totalTokens,
                'input_tokens' => $inputTokens,
                'output_tokens' => $outputTokens,
                'cost' => $cost,
                'duration_ms' => $duration,
                'status_code' => $statusCode,
                'error_message' => $statusCode >= 400 ? ($responseData['error'] ?? 'Unknown error') : null,
                'request_id' => $responseData['id'] ?? null,
            ]);
        } catch (Exception $e) {
            Log::error('Failed to log request', [
                'error' => $e->getMessage(),
                'api_key_id' => $apiKey->id,
                'model_id' => $llmModel->id,
            ]);
        }
    }

    /**
     * Calculate token usage when not provided by the API.
     */
    private function calculateTokenUsage(array $requestData, array $responseData, LlmModel $llmModel, string $endpoint): array
    {
        $inputTokens = 0;
        $outputTokens = 0;

        try {
            if ($endpoint === '/v1/chat/completions' || $endpoint === '/v1/responses') {
                // Calculate input tokens from messages
                if (isset($requestData['messages'])) {
                    $chatTokens = $this->tokenizationService->countChatTokens($requestData['messages'], $llmModel);
                    $inputTokens = $chatTokens['total_tokens'];
                }

                // Calculate output tokens from response
                if (isset($responseData['choices'][0]['message']['content'])) {
                    $outputTokens = $this->tokenizationService->countTokens(
                        $responseData['choices'][0]['message']['content'],
                        $llmModel
                    );
                }
            } elseif ($endpoint === '/v1/completions') {
                // Calculate input tokens from prompt
                if (isset($requestData['prompt'])) {
                    $inputTokens = $this->tokenizationService->countTokens($requestData['prompt'], $llmModel);
                }

                // Calculate output tokens from response
                if (isset($responseData['choices'][0]['text'])) {
                    $outputTokens = $this->tokenizationService->countTokens(
                        $responseData['choices'][0]['text'],
                        $llmModel
                    );
                }
            }
        } catch (Exception $e) {
            Log::warning('Failed to calculate token usage', [
                'error' => $e->getMessage(),
                'model' => $llmModel->model_identifier,
                'endpoint' => $endpoint,
            ]);
        }

        return [
            'input_tokens' => $inputTokens,
            'output_tokens' => $outputTokens,
        ];
    }

    /**
     * Return an error response in OpenAI API format.
     */
    private function errorResponse(string $message, int $status): JsonResponse
    {
        return response()->json([
            'error' => [
                'message' => $message,
                'type' => 'invalid_request_error',
                'param' => null,
                'code' => null,
            ]
        ], $status);
    }

    /**
     * Get available models for a user.
     */
    public function getAvailableModels(User $user): array
    {
        $models = $user->accessibleLlmModels()
            ->with('provider')
            ->get();

        return $models->map(function ($model) {
            return [
                'id' => $model->model_identifier,
                'object' => 'model',
                'created' => $model->created_at->timestamp,
                'owned_by' => strtolower($model->provider->name),
                'permission' => [],
                'root' => $model->model_identifier,
                'parent' => null,
            ];
        })->toArray();
    }
}
