<?php

namespace App\Services\Providers;

use App\Models\LlmModel;

class AnthropicAdapter extends BaseProviderAdapter
{
    /**
     * Get the headers required for API requests.
     */
    public function getHeaders(): array
    {
        return [
            'x-api-key' => $this->provider->api_key,
            'Content-Type' => 'application/json',
            'anthropic-version' => $this->provider->config['version'] ?? '2023-06-01',
        ];
    }

    /**
     * Transform OpenAI-compatible request to Anthropic format.
     */
    public function transformRequest(array $requestData, LlmModel $model): array
    {
        $anthropicRequest = [
            'model' => $model->model_identifier,
            'max_tokens' => $requestData['max_tokens'] ?? 4096,
        ];

        // Handle messages
        if (isset($requestData['messages'])) {
            $messages = $requestData['messages'];
            
            // Extract system message if present
            $systemMessage = null;
            $conversationMessages = [];
            
            foreach ($messages as $message) {
                if ($message['role'] === 'system') {
                    $systemMessage = $message['content'];
                } else {
                    $conversationMessages[] = [
                        'role' => $message['role'],
                        'content' => $message['content'],
                    ];
                }
            }
            
            if ($systemMessage) {
                $anthropicRequest['system'] = $systemMessage;
            }
            
            $anthropicRequest['messages'] = $conversationMessages;
        }

        // Handle other parameters
        if (isset($requestData['temperature'])) {
            $anthropicRequest['temperature'] = $requestData['temperature'];
        }

        if (isset($requestData['top_p'])) {
            $anthropicRequest['top_p'] = $requestData['top_p'];
        }

        if (isset($requestData['stream'])) {
            $anthropicRequest['stream'] = $requestData['stream'];
        }

        if (isset($requestData['stop'])) {
            $anthropicRequest['stop_sequences'] = is_array($requestData['stop']) 
                ? $requestData['stop'] 
                : [$requestData['stop']];
        }

        return $anthropicRequest;
    }

    /**
     * Transform Anthropic response to OpenAI-compatible format.
     */
    public function transformResponse(array $responseData, LlmModel $model): array
    {
        // Handle different response types
        if (isset($responseData['type']) && $responseData['type'] === 'message') {
            return $this->transformMessageResponse($responseData, $model);
        }

        // Handle error responses
        if (isset($responseData['error'])) {
            return [
                'error' => [
                    'message' => $responseData['error']['message'] ?? 'Unknown error',
                    'type' => $responseData['error']['type'] ?? 'api_error',
                    'param' => null,
                    'code' => null,
                ]
            ];
        }

        return $responseData;
    }

    /**
     * Transform Anthropic message response to OpenAI chat completion format.
     */
    private function transformMessageResponse(array $responseData, LlmModel $model): array
    {
        $content = '';
        if (isset($responseData['content']) && is_array($responseData['content'])) {
            foreach ($responseData['content'] as $contentBlock) {
                if ($contentBlock['type'] === 'text') {
                    $content .= $contentBlock['text'];
                }
            }
        }

        return [
            'id' => 'chatcmpl-' . bin2hex(random_bytes(12)),
            'object' => 'chat.completion',
            'created' => $this->getCurrentTimestamp(),
            'model' => $model->model_identifier,
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $content,
                    ],
                    'finish_reason' => $this->mapStopReason($responseData['stop_reason'] ?? null),
                ]
            ],
            'usage' => [
                'prompt_tokens' => $responseData['usage']['input_tokens'] ?? 0,
                'completion_tokens' => $responseData['usage']['output_tokens'] ?? 0,
                'total_tokens' => ($responseData['usage']['input_tokens'] ?? 0) + ($responseData['usage']['output_tokens'] ?? 0),
            ],
        ];
    }

    /**
     * Map Anthropic stop reason to OpenAI finish reason.
     */
    private function mapStopReason(?string $stopReason): string
    {
        return match ($stopReason) {
            'end_turn' => 'stop',
            'max_tokens' => 'length',
            'stop_sequence' => 'stop',
            'tool_use' => 'tool_calls',
            default => 'stop',
        };
    }

    /**
     * Map endpoint to Anthropic-specific endpoint.
     */
    protected function mapEndpoint(string $endpoint): string
    {
        return match ($endpoint) {
            '/v1/chat/completions' => '/v1/messages',
            '/v1/completions' => '/v1/complete', // Legacy endpoint
            '/v1/responses' => '/v1/messages', // Map responses to messages for Anthropic
            default => $endpoint,
        };
    }

    /**
     * Build the full endpoint URL.
     */
    protected function buildEndpointUrl(string $endpoint, LlmModel $model): string
    {
        $mappedEndpoint = $this->mapEndpoint($endpoint);
        $baseUrl = rtrim($this->getBaseUrl(), '/');
        $endpoint = ltrim($mappedEndpoint, '/');
        
        return "{$baseUrl}/{$endpoint}";
    }
}
