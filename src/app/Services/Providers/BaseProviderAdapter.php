<?php

namespace App\Services\Providers;

use App\Models\Provider;
use App\Models\LlmModel;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

abstract class BaseProviderAdapter implements ProviderAdapterInterface
{
    protected Provider $provider;
    protected Client $httpClient;

    public function __construct(Provider $provider)
    {
        $this->provider = $provider;
        $this->httpClient = new Client([
            'timeout' => 120,
            'connect_timeout' => 10,
        ]);
    }

    /**
     * Make a request to the provider's API.
     */
    public function makeRequest(string $endpoint, array $requestData, LlmModel $model): array
    {
        try {
            // Transform the request to provider-specific format
            $transformedRequest = $this->transformRequest($requestData, $model);
            
            // Determine the actual endpoint URL
            $url = $this->buildEndpointUrl($endpoint, $model);
            
            // Make the HTTP request
            $response = $this->httpClient->request('POST', $url, [
                'headers' => $this->getHeaders(),
                'json' => $transformedRequest,
            ]);

            $responseBody = json_decode($response->getBody()->getContents(), true);
            
            // Transform the response to OpenAI-compatible format
            $transformedResponse = $this->transformResponse($responseBody, $model);

            return [
                'body' => $transformedResponse,
                'status' => $response->getStatusCode(),
            ];

        } catch (RequestException $e) {
            Log::error('Provider API request failed', [
                'provider' => $this->provider->name,
                'model' => $model->model_identifier,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
            ]);

            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 500;
            $errorBody = $e->getResponse() ? 
                json_decode($e->getResponse()->getBody()->getContents(), true) : 
                ['error' => ['message' => 'Request failed']];

            return [
                'body' => $this->transformErrorResponse($errorBody),
                'status' => $statusCode,
            ];
        }
    }

    /**
     * Build the full endpoint URL.
     */
    protected function buildEndpointUrl(string $endpoint, LlmModel $model): string
    {
        $baseUrl = rtrim($this->getBaseUrl(), '/');
        $endpoint = ltrim($endpoint, '/');
        
        return "{$baseUrl}/{$endpoint}";
    }

    /**
     * Get the provider's base URL.
     */
    public function getBaseUrl(): string
    {
        return $this->provider->base_url;
    }

    /**
     * Transform error response to OpenAI-compatible format.
     */
    protected function transformErrorResponse(array $errorResponse): array
    {
        return [
            'error' => [
                'message' => $errorResponse['error']['message'] ?? 'Unknown error',
                'type' => $errorResponse['error']['type'] ?? 'api_error',
                'param' => $errorResponse['error']['param'] ?? null,
                'code' => $errorResponse['error']['code'] ?? null,
            ]
        ];
    }

    /**
     * Generate a unique request ID.
     */
    protected function generateRequestId(): string
    {
        return 'req_' . bin2hex(random_bytes(12));
    }

    /**
     * Get current timestamp.
     */
    protected function getCurrentTimestamp(): int
    {
        return time();
    }

    /**
     * Map endpoint to provider-specific endpoint.
     */
    abstract protected function mapEndpoint(string $endpoint): string;
}
