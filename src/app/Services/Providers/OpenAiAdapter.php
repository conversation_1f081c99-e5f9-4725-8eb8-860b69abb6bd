<?php

namespace App\Services\Providers;

use App\Models\LlmModel;

class OpenAiAdapter extends BaseProviderAdapter
{
    /**
     * Get the headers required for API requests.
     */
    public function getHeaders(): array
    {
        $headers = [
            'Authorization' => 'Bearer ' . $this->provider->api_key,
            'Content-Type' => 'application/json',
        ];

        // Add organization header if configured
        if (isset($this->provider->config['organization'])) {
            $headers['OpenAI-Organization'] = $this->provider->config['organization'];
        }

        return $headers;
    }

    /**
     * Transform OpenAI-compatible request to OpenAI format (pass-through).
     */
    public function transformRequest(array $requestData, LlmModel $model): array
    {
        // For OpenAI, we mostly pass through the request as-is
        // but ensure the model identifier is correct
        $requestData['model'] = $model->model_identifier;
        
        return $requestData;
    }

    /**
     * Transform OpenAI response to OpenAI-compatible format (pass-through).
     */
    public function transformResponse(array $responseData, LlmModel $model): array
    {
        // OpenAI responses are already in the correct format
        return $responseData;
    }

    /**
     * Map endpoint to OpenAI-specific endpoint.
     */
    protected function mapEndpoint(string $endpoint): string
    {
        // OpenAI endpoints map directly
        return match ($endpoint) {
            '/v1/chat/completions' => '/v1/chat/completions',
            '/v1/completions' => '/v1/completions',
            '/v1/responses' => '/v1/responses',
            '/v1/models' => '/v1/models',
            default => $endpoint,
        };
    }

    /**
     * Build the full endpoint URL.
     */
    protected function buildEndpointUrl(string $endpoint, LlmModel $model): string
    {
        $mappedEndpoint = $this->mapEndpoint($endpoint);
        $baseUrl = rtrim($this->getBaseUrl(), '/');
        $endpoint = ltrim($mappedEndpoint, '/');
        
        return "{$baseUrl}/{$endpoint}";
    }
}
