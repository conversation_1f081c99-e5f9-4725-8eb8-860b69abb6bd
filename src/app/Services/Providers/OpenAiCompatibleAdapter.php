<?php

namespace App\Services\Providers;

use App\Models\LlmModel;

class OpenAiCompatibleAdapter extends BaseProviderAdapter
{
    /**
     * Get the headers required for API requests.
     */
    public function getHeaders(): array
    {
        $headers = [
            'Authorization' => 'Bearer ' . $this->provider->api_key,
            'Content-Type' => 'application/json',
        ];

        // Add provider-specific headers from config
        if (isset($this->provider->config['headers'])) {
            $headers = array_merge($headers, $this->provider->config['headers']);
        }

        // Add common OpenRouter headers if configured
        if (isset($this->provider->config['site_url'])) {
            $headers['HTTP-Referer'] = $this->provider->config['site_url'];
        }

        if (isset($this->provider->config['app_name'])) {
            $headers['X-Title'] = $this->provider->config['app_name'];
        }

        return $headers;
    }

    /**
     * Transform OpenAI-compatible request (mostly pass-through).
     */
    public function transformRequest(array $requestData, LlmModel $model): array
    {
        // For OpenAI-compatible providers, we mostly pass through the request
        // but ensure the model identifier is correct
        $requestData['model'] = $model->model_identifier;
        
        // Apply any provider-specific transformations from config
        if (isset($this->provider->config['request_transforms'])) {
            foreach ($this->provider->config['request_transforms'] as $transform) {
                $requestData = $this->applyTransform($requestData, $transform);
            }
        }
        
        return $requestData;
    }

    /**
     * Transform provider response to OpenAI-compatible format.
     */
    public function transformResponse(array $responseData, LlmModel $model): array
    {
        // Most OpenAI-compatible providers return responses in the correct format
        // but we may need to apply some normalization
        
        // Ensure the model field is set correctly
        if (isset($responseData['model'])) {
            $responseData['model'] = $model->model_identifier;
        }

        // Apply any provider-specific response transformations from config
        if (isset($this->provider->config['response_transforms'])) {
            foreach ($this->provider->config['response_transforms'] as $transform) {
                $responseData = $this->applyTransform($responseData, $transform);
            }
        }

        return $responseData;
    }

    /**
     * Apply a transformation to data based on configuration.
     */
    private function applyTransform(array $data, array $transform): array
    {
        switch ($transform['type']) {
            case 'rename_field':
                if (isset($data[$transform['from']])) {
                    $data[$transform['to']] = $data[$transform['from']];
                    unset($data[$transform['from']]);
                }
                break;
                
            case 'set_field':
                $data[$transform['field']] = $transform['value'];
                break;
                
            case 'remove_field':
                unset($data[$transform['field']]);
                break;
                
            case 'map_values':
                if (isset($data[$transform['field']])) {
                    $mapping = $transform['mapping'];
                    if (isset($mapping[$data[$transform['field']]])) {
                        $data[$transform['field']] = $mapping[$data[$transform['field']]];
                    }
                }
                break;
        }
        
        return $data;
    }

    /**
     * Map endpoint to provider-specific endpoint.
     */
    protected function mapEndpoint(string $endpoint): string
    {
        // Check if there are custom endpoint mappings in the provider config
        if (isset($this->provider->config['endpoint_mappings'])) {
            $mappings = $this->provider->config['endpoint_mappings'];
            if (isset($mappings[$endpoint])) {
                return $mappings[$endpoint];
            }
        }

        // Default OpenAI-compatible mappings
        return match ($endpoint) {
            '/v1/chat/completions' => '/v1/chat/completions',
            '/v1/completions' => '/v1/completions',
            '/v1/responses' => '/v1/responses',
            '/v1/models' => '/v1/models',
            default => $endpoint,
        };
    }

    /**
     * Build the full endpoint URL.
     */
    protected function buildEndpointUrl(string $endpoint, LlmModel $model): string
    {
        $mappedEndpoint = $this->mapEndpoint($endpoint);
        $baseUrl = rtrim($this->getBaseUrl(), '/');
        $endpoint = ltrim($mappedEndpoint, '/');
        
        return "{$baseUrl}/{$endpoint}";
    }
}
