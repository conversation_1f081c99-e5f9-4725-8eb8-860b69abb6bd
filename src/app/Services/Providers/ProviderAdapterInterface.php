<?php

namespace App\Services\Providers;

use App\Models\LlmModel;

interface ProviderAdapterInterface
{
    /**
     * Make a request to the provider's API.
     *
     * @param string $endpoint The API endpoint (e.g., '/v1/chat/completions')
     * @param array $requestData The request payload
     * @param LlmModel $model The LLM model being used
     * @return array Response with 'body' and 'status' keys
     */
    public function makeRequest(string $endpoint, array $requestData, LlmModel $model): array;

    /**
     * Transform OpenAI-compatible request to provider-specific format.
     *
     * @param array $requestData OpenAI-compatible request data
     * @param LlmModel $model The LLM model being used
     * @return array Provider-specific request data
     */
    public function transformRequest(array $requestData, LlmModel $model): array;

    /**
     * Transform provider response to OpenAI-compatible format.
     *
     * @param array $responseData Provider-specific response data
     * @param LlmModel $model The LLM model being used
     * @return array OpenAI-compatible response data
     */
    public function transformResponse(array $responseData, LlmModel $model): array;

    /**
     * Get the provider's base URL for API requests.
     *
     * @return string
     */
    public function getBaseUrl(): string;

    /**
     * Get the headers required for API requests.
     *
     * @return array
     */
    public function getHeaders(): array;
}
