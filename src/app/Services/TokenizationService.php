<?php

namespace App\Services;

use App\Models\LlmModel;
use Yethee\Tiktoken\EncoderProvider;

class TokenizationService
{
    private array $encoders = [];

    /**
     * Count tokens for a given text and model.
     */
    public function countTokens(string $text, LlmModel $model): int
    {
        return match ($model->provider->type) {
            'openai' => $this->countOpenAiTokens($text, $model),
            'anthropic' => $this->countAnthropicTokens($text, $model),
            'openai_compatible' => $this->countOpenAiCompatibleTokens($text, $model),
            default => $this->estimateTokens($text),
        };
    }

    /**
     * Count tokens for OpenAI models using tiktoken.
     */
    private function countOpenAiTokens(string $text, LlmModel $model): int
    {
        $encoding = $this->getOpenAiEncoding($model->model_identifier);

        if (!isset($this->encoders[$encoding])) {
            $provider = new EncoderProvider();
            $this->encoders[$encoding] = $provider->get($encoding);
        }

        return count($this->encoders[$encoding]->encode($text));
    }

    /**
     * Count tokens for Anthropic models (approximation based on their tokenizer).
     */
    private function countAnthropicTokens(string $text, LlmModel $model): int
    {
        // Anthropic uses a similar tokenizer to OpenAI for Claude models
        // We'll use cl100k_base as a reasonable approximation
        if (!isset($this->encoders['cl100k_base'])) {
            $provider = new EncoderProvider();
            $this->encoders['cl100k_base'] = $provider->get('cl100k_base');
        }

        return count($this->encoders['cl100k_base']->encode($text));
    }

    /**
     * Count tokens for OpenAI-compatible models.
     */
    private function countOpenAiCompatibleTokens(string $text, LlmModel $model): int
    {
        // For OpenAI-compatible models, try to determine the best tokenizer
        $modelId = strtolower($model->model_identifier);
        
        if (str_contains($modelId, 'llama') || str_contains($modelId, 'meta')) {
            // Llama models use a different tokenizer, approximate with cl100k_base
            return $this->countAnthropicTokens($text, $model);
        }
        
        if (str_contains($modelId, 'gpt')) {
            // GPT-based models on other providers
            return $this->countOpenAiTokens($text, $model);
        }

        // Default to estimation
        return $this->estimateTokens($text);
    }

    /**
     * Get the appropriate encoding for OpenAI models.
     */
    private function getOpenAiEncoding(string $modelIdentifier): string
    {
        return match (true) {
            str_contains($modelIdentifier, 'gpt-4') => 'cl100k_base',
            str_contains($modelIdentifier, 'gpt-3.5') => 'cl100k_base',
            str_contains($modelIdentifier, 'text-embedding') => 'cl100k_base',
            str_contains($modelIdentifier, 'text-davinci') => 'p50k_base',
            str_contains($modelIdentifier, 'code-davinci') => 'p50k_base',
            default => 'cl100k_base', // Default for newer models
        };
    }

    /**
     * Estimate tokens using a simple heuristic (fallback method).
     */
    private function estimateTokens(string $text): int
    {
        // Rough estimation: 1 token ≈ 4 characters for English text
        // This is a fallback when proper tokenization isn't available
        return (int) ceil(strlen($text) / 4);
    }

    /**
     * Count tokens for chat messages format.
     */
    public function countChatTokens(array $messages, LlmModel $model): array
    {
        $totalTokens = 0;
        $messageTokens = [];

        foreach ($messages as $message) {
            $content = $message['content'] ?? '';
            $role = $message['role'] ?? 'user';
            
            // Count content tokens
            $contentTokens = $this->countTokens($content, $model);
            
            // Add overhead for message formatting (varies by model)
            $overhead = $this->getMessageOverhead($model, $role);
            $messageTotal = $contentTokens + $overhead;
            
            $messageTokens[] = [
                'role' => $role,
                'content_tokens' => $contentTokens,
                'overhead_tokens' => $overhead,
                'total_tokens' => $messageTotal,
            ];
            
            $totalTokens += $messageTotal;
        }

        return [
            'total_tokens' => $totalTokens,
            'messages' => $messageTokens,
        ];
    }

    /**
     * Get message formatting overhead tokens.
     */
    private function getMessageOverhead(LlmModel $model, string $role): int
    {
        // Different models have different overhead for message formatting
        return match ($model->provider->type) {
            'openai' => match ($role) {
                'system' => 4,
                'user' => 4,
                'assistant' => 4,
                'tool' => 4,
                default => 4,
            },
            'anthropic' => match ($role) {
                'system' => 0, // System messages are handled differently
                'user' => 3,
                'assistant' => 3,
                default => 3,
            },
            default => 3,
        };
    }

    /**
     * Estimate completion tokens based on max_tokens parameter.
     */
    public function estimateCompletionTokens(?int $maxTokens, LlmModel $model): int
    {
        if ($maxTokens) {
            return min($maxTokens, $model->max_tokens ?? 4096);
        }

        // Default estimation based on model type
        return match ($model->provider->type) {
            'openai' => 150, // Typical completion length
            'anthropic' => 200,
            default => 150,
        };
    }
}
