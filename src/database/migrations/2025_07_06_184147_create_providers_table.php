<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('providers', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('name');
            $table->enum('type', ['anthropic', 'openai', 'openai_compatible']);
            $table->string('base_url');
            $table->text('api_key'); // Will be encrypted
            $table->boolean('is_active')->default(true);
            $table->json('config')->nullable(); // Additional provider-specific configuration
            $table->timestamps();

            $table->unique(['name']);
            $table->index(['type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('providers');
    }
};
