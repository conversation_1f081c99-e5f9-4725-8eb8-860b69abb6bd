<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('llm_models', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('provider_id')->constrained('providers')->onDelete('cascade');
            $table->string('name'); // Display name (e.g., "Claude 3.5 Sonnet")
            $table->string('model_identifier'); // API identifier (e.g., "claude-3-5-sonnet-20241022")
            $table->boolean('is_active')->default(true);
            $table->integer('max_tokens')->nullable();
            $table->decimal('cost_per_input_token', 10, 8)->nullable(); // Cost per 1000 input tokens
            $table->decimal('cost_per_output_token', 10, 8)->nullable(); // Cost per 1000 output tokens
            $table->json('capabilities')->nullable(); // e.g., ["chat", "completion", "vision"]
            $table->json('config')->nullable(); // Model-specific configuration
            $table->timestamps();

            $table->unique(['provider_id', 'model_identifier']);
            $table->index(['provider_id', 'is_active']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('llm_models');
    }
};
