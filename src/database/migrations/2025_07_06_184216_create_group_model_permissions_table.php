<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('group_model_permissions', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('group_id')->constrained('groups')->onDelete('cascade');
            $table->foreignUlid('llm_model_id')->constrained('llm_models')->onDelete('cascade');
            $table->boolean('can_access')->default(true);
            $table->json('restrictions')->nullable(); // Rate limits, usage quotas, etc.
            $table->timestamps();

            $table->unique(['group_id', 'llm_model_id']);
            $table->index(['group_id', 'can_access']);
            $table->index(['llm_model_id', 'can_access']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('group_model_permissions');
    }
};
