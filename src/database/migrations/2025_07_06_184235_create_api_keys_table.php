<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_keys', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('name'); // User-friendly name for the API key
            $table->string('key_hash'); // Hashed version of the API key
            $table->string('key_prefix', 8); // First 8 characters for identification
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('permissions')->nullable(); // Additional key-specific permissions
            $table->timestamps();

            $table->unique(['key_hash']);
            $table->unique(['key_prefix', 'user_id']); // Ensure unique prefix per user
            $table->index(['user_id', 'is_active']);
            $table->index(['is_active', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_keys');
    }
};
