<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('request_logs', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('api_key_id')->constrained('api_keys')->onDelete('cascade');
            $table->foreignUlid('provider_id')->constrained('providers')->onDelete('cascade');
            $table->foreignUlid('llm_model_id')->constrained('llm_models')->onDelete('cascade');
            $table->string('endpoint'); // e.g., '/v1/chat/completions'
            $table->string('method', 10); // HTTP method
            $table->json('request_data')->nullable(); // Request payload (may be large)
            $table->json('response_data')->nullable(); // Response payload (may be large)
            $table->integer('tokens_used')->nullable(); // Total tokens consumed
            $table->integer('input_tokens')->nullable(); // Input tokens
            $table->integer('output_tokens')->nullable(); // Output tokens
            $table->decimal('cost', 10, 6)->nullable(); // Cost in USD
            $table->integer('duration_ms')->nullable(); // Request duration in milliseconds
            $table->integer('status_code'); // HTTP status code
            $table->string('error_message')->nullable(); // Error message if any
            $table->string('request_id')->nullable(); // Provider's request ID for tracking
            $table->timestamps();

            $table->index(['api_key_id', 'created_at']);
            $table->index(['provider_id', 'created_at']);
            $table->index(['llm_model_id', 'created_at']);
            $table->index(['status_code', 'created_at']);
            $table->index(['created_at']); // For time-based queries
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('request_logs');
    }
};
