<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create test user
        $testUser = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Create admin user
        $adminUser = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);

        // Seed providers and models
        $this->call([
            ProviderSeeder::class,
            GroupSeeder::class,
        ]);

        // Add users to groups using the UserGroup model
        $adminGroup = \App\Models\Group::where('name', 'Administrators')->first();
        $basicGroup = \App\Models\Group::where('name', 'Basic Users')->first();

        if ($adminGroup) {
            \App\Models\UserGroup::create([
                'user_id' => $adminUser->id,
                'group_id' => $adminGroup->id,
            ]);
        }

        if ($basicGroup) {
            \App\Models\UserGroup::create([
                'user_id' => $testUser->id,
                'group_id' => $basicGroup->id,
            ]);
        }
    }
}
