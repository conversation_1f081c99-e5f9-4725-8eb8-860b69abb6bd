<?php

namespace Database\Seeders;

use App\Models\Group;
use App\Models\LlmModel;
use App\Models\GroupModelPermission;
use Illuminate\Database\Seeder;

class GroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin group with access to all models
        $adminGroup = Group::create([
            'name' => 'Administrators',
            'description' => 'Full access to all LLM models and administrative functions',
            'is_active' => true,
            'permissions' => [
                'admin_access' => true,
                'manage_users' => true,
                'manage_groups' => true,
                'manage_providers' => true,
                'view_analytics' => true,
            ],
        ]);

        // Create Developer group with access to most models
        $developerGroup = Group::create([
            'name' => 'Developers',
            'description' => 'Access to development and testing models',
            'is_active' => true,
            'permissions' => [
                'admin_access' => false,
                'view_analytics' => true,
            ],
        ]);

        // Create Basic Users group with limited access
        $basicGroup = Group::create([
            'name' => 'Basic Users',
            'description' => 'Limited access to basic models',
            'is_active' => true,
            'permissions' => [
                'admin_access' => false,
                'view_analytics' => false,
            ],
        ]);

        // Get all models
        $allModels = LlmModel::all();

        // Give admin group access to all models
        foreach ($allModels as $model) {
            GroupModelPermission::create([
                'group_id' => $adminGroup->id,
                'llm_model_id' => $model->id,
                'can_access' => true,
                'restrictions' => [
                    'rate_limit_per_minute' => null, // No limits
                    'daily_token_limit' => null,
                    'monthly_cost_limit' => null,
                ],
            ]);
        }

        // Give developer group access to most models (exclude the most expensive ones)
        $developerModels = $allModels->filter(function ($model) {
            return $model->cost_per_input_token <= 0.003; // Exclude very expensive models
        });

        foreach ($developerModels as $model) {
            GroupModelPermission::create([
                'group_id' => $developerGroup->id,
                'llm_model_id' => $model->id,
                'can_access' => true,
                'restrictions' => [
                    'rate_limit_per_minute' => 60,
                    'daily_token_limit' => 100000,
                    'monthly_cost_limit' => 100.00,
                ],
            ]);
        }

        // Give basic group access to only the cheapest models
        $basicModels = $allModels->filter(function ($model) {
            return $model->cost_per_input_token <= 0.001; // Only very cheap models
        });

        foreach ($basicModels as $model) {
            GroupModelPermission::create([
                'group_id' => $basicGroup->id,
                'llm_model_id' => $model->id,
                'can_access' => true,
                'restrictions' => [
                    'rate_limit_per_minute' => 10,
                    'daily_token_limit' => 10000,
                    'monthly_cost_limit' => 10.00,
                ],
            ]);
        }
    }
}
