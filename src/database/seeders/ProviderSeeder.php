<?php

namespace Database\Seeders;

use App\Models\Provider;
use App\Models\LlmModel;
use Illuminate\Database\Seeder;

class ProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Anthropic provider
        $anthropic = Provider::create([
            'name' => 'Anthropic',
            'type' => 'anthropic',
            'base_url' => 'https://api.anthropic.com',
            'api_key' => 'your-anthropic-api-key-here', // This will be encrypted
            'is_active' => true,
            'config' => [
                'version' => '2023-06-01',
                'max_retries' => 3,
            ],
        ]);

        // Create OpenAI provider
        $openai = Provider::create([
            'name' => 'OpenAI',
            'type' => 'openai',
            'base_url' => 'https://api.openai.com',
            'api_key' => 'your-openai-api-key-here', // This will be encrypted
            'is_active' => true,
            'config' => [
                'organization' => null,
                'max_retries' => 3,
            ],
        ]);

        // Create OpenRouter provider (OpenAI-compatible)
        $openrouter = Provider::create([
            'name' => 'OpenRouter',
            'type' => 'openai_compatible',
            'base_url' => 'https://openrouter.ai/api',
            'api_key' => 'your-openrouter-api-key-here', // This will be encrypted
            'is_active' => true,
            'config' => [
                'site_url' => 'https://your-app.com',
                'app_name' => 'LLM Router',
            ],
        ]);

        // Create Anthropic models (Updated pricing as of December 2024)
        LlmModel::create([
            'provider_id' => $anthropic->id,
            'name' => 'Claude 3.5 Sonnet',
            'model_identifier' => 'claude-3-5-sonnet-20241022',
            'is_active' => true,
            'max_tokens' => 200000,
            'cost_per_input_token' => 0.003000, // $3.00 per 1M input tokens
            'cost_per_output_token' => 0.015000, // $15.00 per 1M output tokens
            'capabilities' => ['chat', 'vision', 'tool_use'],
            'config' => [
                'supports_system_messages' => true,
                'supports_streaming' => true,
                'tokenizer_type' => 'anthropic',
            ],
        ]);

        LlmModel::create([
            'provider_id' => $anthropic->id,
            'name' => 'Claude 3.5 Haiku',
            'model_identifier' => 'claude-3-5-haiku-20241022',
            'is_active' => true,
            'max_tokens' => 200000,
            'cost_per_input_token' => 0.000800, // $0.80 per 1M input tokens
            'cost_per_output_token' => 0.004000, // $4.00 per 1M output tokens
            'capabilities' => ['chat', 'vision'],
            'config' => [
                'supports_system_messages' => true,
                'supports_streaming' => true,
                'tokenizer_type' => 'anthropic',
            ],
        ]);

        // Create OpenAI models (Updated pricing as of December 2024)
        LlmModel::create([
            'provider_id' => $openai->id,
            'name' => 'GPT-4o',
            'model_identifier' => 'gpt-4o',
            'is_active' => true,
            'max_tokens' => 128000,
            'cost_per_input_token' => 0.002500, // $2.50 per 1M input tokens
            'cost_per_output_token' => 0.010000, // $10.00 per 1M output tokens
            'capabilities' => ['chat', 'vision', 'tool_use'],
            'config' => [
                'supports_system_messages' => true,
                'supports_streaming' => true,
                'supports_functions' => true,
                'tokenizer_type' => 'openai',
                'encoding' => 'cl100k_base',
            ],
        ]);

        LlmModel::create([
            'provider_id' => $openai->id,
            'name' => 'GPT-4o Mini',
            'model_identifier' => 'gpt-4o-mini',
            'is_active' => true,
            'max_tokens' => 128000,
            'cost_per_input_token' => 0.000150, // $0.15 per 1M input tokens
            'cost_per_output_token' => 0.000600, // $0.60 per 1M output tokens
            'capabilities' => ['chat', 'vision', 'tool_use'],
            'config' => [
                'supports_system_messages' => true,
                'supports_streaming' => true,
                'supports_functions' => true,
                'tokenizer_type' => 'openai',
                'encoding' => 'cl100k_base',
            ],
        ]);

        // Create some OpenRouter models (Updated pricing as of December 2024)
        LlmModel::create([
            'provider_id' => $openrouter->id,
            'name' => 'Meta Llama 3.1 405B Instruct',
            'model_identifier' => 'meta-llama/llama-3.1-405b-instruct',
            'is_active' => true,
            'max_tokens' => 131072,
            'cost_per_input_token' => 0.002700, // $2.70 per 1M input tokens
            'cost_per_output_token' => 0.002700, // $2.70 per 1M output tokens
            'capabilities' => ['chat', 'tool_use'],
            'config' => [
                'supports_system_messages' => true,
                'supports_streaming' => true,
                'tokenizer_type' => 'llama',
            ],
        ]);

        LlmModel::create([
            'provider_id' => $openrouter->id,
            'name' => 'Meta Llama 3.1 70B Instruct',
            'model_identifier' => 'meta-llama/llama-3.1-70b-instruct',
            'is_active' => true,
            'max_tokens' => 131072,
            'cost_per_input_token' => 0.000520, // $0.52 per 1M input tokens
            'cost_per_output_token' => 0.000520, // $0.52 per 1M output tokens
            'capabilities' => ['chat', 'tool_use'],
            'config' => [
                'supports_system_messages' => true,
                'supports_streaming' => true,
                'tokenizer_type' => 'llama',
            ],
        ]);
    }
}
