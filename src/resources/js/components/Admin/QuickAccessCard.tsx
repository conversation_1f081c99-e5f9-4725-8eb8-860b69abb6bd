import React from 'react';
import { Link } from '@inertiajs/react';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

interface QuickAccessCardProps {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  stats?: {
    label: string;
    value: string | number;
  };
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo' | 'pink' | 'orange';
}

const colorClasses = {
  blue: {
    bg: 'bg-blue-50',
    icon: 'text-blue-600',
    border: 'border-blue-200',
    hover: 'hover:bg-blue-100',
  },
  green: {
    bg: 'bg-green-50',
    icon: 'text-green-600',
    border: 'border-green-200',
    hover: 'hover:bg-green-100',
  },
  yellow: {
    bg: 'bg-yellow-50',
    icon: 'text-yellow-600',
    border: 'border-yellow-200',
    hover: 'hover:bg-yellow-100',
  },
  red: {
    bg: 'bg-red-50',
    icon: 'text-red-600',
    border: 'border-red-200',
    hover: 'hover:bg-red-100',
  },
  purple: {
    bg: 'bg-purple-50',
    icon: 'text-purple-600',
    border: 'border-purple-200',
    hover: 'hover:bg-purple-100',
  },
  indigo: {
    bg: 'bg-indigo-50',
    icon: 'text-indigo-600',
    border: 'border-indigo-200',
    hover: 'hover:bg-indigo-100',
  },
  pink: {
    bg: 'bg-pink-50',
    icon: 'text-pink-600',
    border: 'border-pink-200',
    hover: 'hover:bg-pink-100',
  },
  orange: {
    bg: 'bg-orange-50',
    icon: 'text-orange-600',
    border: 'border-orange-200',
    hover: 'hover:bg-orange-100',
  },
};

export default function QuickAccessCard({
  title,
  description,
  href,
  icon,
  stats,
  color = 'blue',
}: QuickAccessCardProps) {
  const colors = colorClasses[color];

  return (
    <Link
      href={href}
      className={`block p-6 bg-white dark:bg-gray-800 border-2 ${colors.border} rounded-lg shadow-sm ${colors.hover} transition-colors duration-200`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center">
            <div className={`p-2 rounded-md ${colors.bg}`}>
              <div className={`h-6 w-6 ${colors.icon}`}>
                {icon}
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">{title}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{description}</p>
            </div>
          </div>
          
          {stats && (
            <div className="mt-4">
              <div className="text-2xl font-semibold text-gray-900">
                {stats.value}
              </div>
              <div className="text-sm text-gray-500">
                {stats.label}
              </div>
            </div>
          )}
        </div>
        
        <ArrowRightIcon className="h-5 w-5 text-gray-400" />
      </div>
    </Link>
  );
}
