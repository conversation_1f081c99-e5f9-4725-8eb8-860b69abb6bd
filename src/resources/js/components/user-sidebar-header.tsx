import { B<PERSON><PERSON><PERSON><PERSON>, Bread<PERSON>rumb<PERSON><PERSON>, Bread<PERSON>rumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { type BreadcrumbItem as BreadcrumbItemType } from '@/types';
import { Fragment } from 'react';

interface UserSidebarHeaderProps {
    breadcrumbs?: BreadcrumbItemType[];
}

export function UserSidebarHeader({ breadcrumbs = [] }: UserSidebarHeaderProps) {
    return (
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
            <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 h-4" />
                {breadcrumbs.length > 0 && (
                    <Breadcrumb>
                        <BreadcrumbList>
                            {breadcrumbs.map((item, index) => (
                                <Fragment key={index}>
                                    <BreadcrumbItem className="hidden md:block">
                                        {item.current ? (
                                            <BreadcrumbPage>{item.name}</BreadcrumbPage>
                                        ) : (
                                            <BreadcrumbLink href={item.href}>{item.name}</BreadcrumbLink>
                                        )}
                                    </BreadcrumbItem>
                                    {index < breadcrumbs.length - 1 && (
                                        <BreadcrumbSeparator className="hidden md:block" />
                                    )}
                                </Fragment>
                            ))}
                        </BreadcrumbList>
                    </Breadcrumb>
                )}
            </div>
        </header>
    );
}
