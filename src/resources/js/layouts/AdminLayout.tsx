import React from 'react';
import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AdminSidebar } from '@/components/admin-sidebar';
import { AdminSidebarHeader } from '@/components/admin-sidebar-header';
import { type BreadcrumbItem } from '@/types';
import type { PropsWithChildren } from 'react';

interface AdminLayoutProps {
    children: React.ReactNode;
    header?: React.ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}

export default function AdminLayout({ 
    children, 
    header, 
    breadcrumbs = [] 
}: PropsWithChildren<AdminLayoutProps>) {
    return (
        <AppShell variant="sidebar">
            <AdminSidebar />
            <AppContent variant="sidebar" className="overflow-x-hidden">
                <AdminSidebarHeader breadcrumbs={breadcrumbs} header={header} />
                {children}
            </AppContent>
        </AppShell>
    );
}
