import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/components/ui/data-table';
import { Badge } from '@/components/ui/badge';
import {
  Cpu,
  DollarSign,
  Users,
  BarChart3,
  Clock,
  AlertTriangle,
  TrendingUp,
  Activity
} from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';

interface AnalyticsData {
  overview: {
    total_requests: number;
    total_tokens: number;
    total_cost: number;
    success_rate: number;
    active_users: number;
    active_models: number;
  };
  usage_by_model: Array<{
    name: string;
    model_identifier: string;
    provider_name: string;
    request_count: number;
    total_tokens: number;
    total_cost: number;
    avg_duration: number;
  }>;
  usage_by_provider: Array<{
    name: string;
    type: string;
    request_count: number;
    total_tokens: number;
    total_cost: number;
    avg_duration: number;
  }>;
  daily_usage: Array<{
    date: string;
    request_count: number;
    total_tokens: number;
    total_cost: number;
  }>;
  top_users: Array<{
    name: string;
    email: string;
    request_count: number;
    total_tokens: number;
    total_cost: number;
  }>;
}

interface AnalyticsDashboardProps {
  analytics: AnalyticsData;
}

export default function AnalyticsDashboard({ analytics }: AnalyticsDashboardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
  };

  const modelTableColumns: ColumnDef<any>[] = [
    {
      accessorKey: 'name',
      header: 'Model',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue('name')}</div>
          <div className="text-sm text-muted-foreground">{row.original.model_identifier}</div>
        </div>
      ),
    },
    {
      accessorKey: 'provider_name',
      header: 'Provider',
      cell: ({ row }) => (
        <Badge variant="outline">{row.getValue('provider_name')}</Badge>
      ),
    },
    {
      accessorKey: 'request_count',
      header: 'Requests',
      cell: ({ row }) => formatNumber(row.getValue('request_count')),
    },
    {
      accessorKey: 'total_tokens',
      header: 'Tokens',
      cell: ({ row }) => formatNumber(row.getValue('total_tokens')),
    },
    {
      accessorKey: 'total_cost',
      header: 'Cost',
      cell: ({ row }) => formatCurrency(row.getValue('total_cost')),
    },
  ];

  const userTableColumns: ColumnDef<any>[] = [
    {
      accessorKey: 'name',
      header: 'User',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue('name')}</div>
          <div className="text-sm text-muted-foreground">{row.original.email}</div>
        </div>
      ),
    },
    {
      accessorKey: 'request_count',
      header: 'Requests',
      cell: ({ row }) => formatNumber(row.getValue('request_count')),
    },
    {
      accessorKey: 'total_tokens',
      header: 'Tokens',
      cell: ({ row }) => formatNumber(row.getValue('total_tokens')),
    },
    {
      accessorKey: 'total_cost',
      header: 'Cost',
      cell: ({ row }) => formatCurrency(row.getValue('total_cost')),
    },
  ];

  return (
    <AdminLayout
      header={
        <div>
          <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            View usage statistics and performance metrics
          </p>
        </div>
      }
    >
      <Head title="Analytics Dashboard" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        {/* Overview Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(analytics.overview.total_requests)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tokens</CardTitle>
              <Cpu className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(analytics.overview.total_tokens)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(analytics.overview.total_cost)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.success_rate.toFixed(1)}%</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.active_users}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Models</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.active_models}</div>
            </CardContent>
          </Card>
        </div>

        {/* Data Tables */}
        <div className="grid gap-4 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Model Usage</CardTitle>
              <CardDescription>Usage statistics by model</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={modelTableColumns}
                data={analytics.usage_by_model}
                searchPlaceholder="Search models..."
                searchColumn="name"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Top Users</CardTitle>
              <CardDescription>Most active users by usage</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={userTableColumns}
                data={analytics.top_users}
                searchPlaceholder="Search users..."
                searchColumn="name"
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
