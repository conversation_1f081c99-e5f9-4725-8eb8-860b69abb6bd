import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface User {
  id: string;
  name: string;
  email: string;
}

interface CreateApiKeyProps {
  users: User[];
}

interface FormData {
  user_id: string;
  name: string;
  expires_at: string;
  permissions: string[];
}

export default function CreateApiKey({ users }: CreateApiKeyProps) {
  const { data, setData, post, processing, errors } = useForm<FormData>({
    user_id: '',
    name: '',
    expires_at: '',
    permissions: [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post('/admin/api-keys');
  };

  const breadcrumbItems = [
    { name: 'API Keys', href: '/admin/api-keys' },
    { name: 'Create', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center space-x-4">
          <Link
            href="/admin/api-keys"
            className="text-gray-400 hover:text-gray-600"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </Link>
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Create API Key
            </h1>
          </div>
        </div>
      }
    >
      <Head title="Create API Key" />

      <div className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* User Selection */}
            <div>
              <label htmlFor="user_id" className="block text-sm font-medium text-gray-700">
                User
              </label>
              <select
                id="user_id"
                value={data.user_id}
                onChange={(e) => setData('user_id', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                required
              >
                <option value="">Select a user</option>
                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name} ({user.email})
                  </option>
                ))}
              </select>
              {errors.user_id && (
                <p className="mt-1 text-sm text-red-600">{errors.user_id}</p>
              )}
            </div>

            {/* API Key Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                API Key Name
              </label>
              <input
                type="text"
                id="name"
                value={data.name}
                onChange={(e) => setData('name', e.target.value)}
                placeholder="e.g., Production API Key, Development Key"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                required
              />
              <p className="mt-1 text-sm text-gray-500">
                Choose a descriptive name to help identify this API key.
              </p>
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            {/* Expiration Date */}
            <div>
              <label htmlFor="expires_at" className="block text-sm font-medium text-gray-700">
                Expiration Date (Optional)
              </label>
              <input
                type="datetime-local"
                id="expires_at"
                value={data.expires_at}
                onChange={(e) => setData('expires_at', e.target.value)}
                min={new Date().toISOString().slice(0, 16)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-sm text-gray-500">
                Leave empty for a key that never expires. For security, consider setting an expiration date.
              </p>
              {errors.expires_at && (
                <p className="mt-1 text-sm text-red-600">{errors.expires_at}</p>
              )}
            </div>

            {/* Security Notice */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Security Notice
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      The API key will be displayed only once after creation. Make sure to copy and store it securely.
                      The user will inherit model access permissions from their assigned groups.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3">
              <Link
                href="/admin/api-keys"
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={processing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {processing ? 'Creating...' : 'Create API Key'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
