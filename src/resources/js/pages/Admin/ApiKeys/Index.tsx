import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Key,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  User
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ColumnDef } from '@tanstack/react-table';

interface ApiKey {
  id: string;
  name: string;
  key_prefix: string;
  is_active: boolean;
  expires_at: string | null;
  last_used_at: string | null;
  created_at: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  request_logs_count: number;
}

interface ApiKeysIndexProps {
  apiKeys: ApiKey[];
}

export default function ApiKeysIndex({ apiKeys }: ApiKeysIndexProps) {
  const handleDelete = (apiKey: ApiKey) => {
    if (confirm(`Are you sure you want to revoke API key "${apiKey.name}"?`)) {
      router.delete(`/admin/api-keys/${apiKey.id}`, {
        onSuccess: () => {
          // Handle success if needed
        },
        onError: (errors) => {
          console.error('Delete failed:', errors);
        },
      });
    }
  };

  const getStatusBadge = (apiKey: ApiKey) => {
    if (!apiKey.is_active) {
      return <Badge variant="destructive">Inactive</Badge>;
    }
    
    if (apiKey.expires_at && new Date(apiKey.expires_at) < new Date()) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    
    return <Badge variant="default">Active</Badge>;
  };

  const columns: ColumnDef<ApiKey>[] = [
    {
      accessorKey: 'name',
      header: 'API Key',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue('name')}</div>
          <div className="text-sm text-muted-foreground font-mono">
            {row.original.key_prefix}...
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'user',
      header: 'User',
      cell: ({ row }) => (
        <div className="flex items-center">
          <User className="h-4 w-4 text-muted-foreground mr-2" />
          <div>
            <div className="font-medium">{row.original.user.name}</div>
            <div className="text-sm text-muted-foreground">{row.original.user.email}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(row.original),
    },
    {
      accessorKey: 'request_logs_count',
      header: 'Requests',
      cell: ({ row }) => (
        <div className="text-sm">
          {(row.getValue('request_logs_count') as number).toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: 'last_used_at',
      header: 'Last Used',
      cell: ({ row }) => {
        const lastUsed = row.getValue('last_used_at') as string | null;
        return (
          <div className="text-sm">
            {lastUsed ? (
              <div className="flex items-center">
                <Clock className="h-4 w-4 text-muted-foreground mr-2" />
                {new Date(lastUsed).toLocaleDateString()}
              </div>
            ) : (
              <span className="text-muted-foreground">Never</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'expires_at',
      header: 'Expires',
      cell: ({ row }) => {
        const expiresAt = row.getValue('expires_at') as string | null;
        return (
          <div className="text-sm">
            {expiresAt ? (
              new Date(expiresAt).toLocaleDateString()
            ) : (
              <span className="text-muted-foreground">Never</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Created',
      cell: ({ row }) => (
        <div className="text-sm">
          {new Date(row.getValue('created_at')).toLocaleDateString()}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const apiKey = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/admin/api-keys/${apiKey.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/api-keys/${apiKey.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDelete(apiKey)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Revoke
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <h1 className="text-2xl font-bold">API Keys</h1>
            <p className="text-muted-foreground">
              View and manage user API keys
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/api-keys/create">
              <Plus className="h-4 w-4 mr-2" />
              Create API Key
            </Link>
          </Button>
        </div>
      }
    >
      <Head title="API Keys" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardContent className="p-6">
            <DataTable
              columns={columns}
              data={apiKeys}
              searchPlaceholder="Search API keys..."
              searchColumn="name"
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
