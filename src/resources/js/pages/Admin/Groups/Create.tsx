import React from 'react';
import { <PERSON>, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save, Users } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface LlmModel {
  id: string;
  name: string;
  model_identifier: string;
  provider: {
    id: string;
    name: string;
    type: string;
  };
}

interface CreateGroupProps {
  models: LlmModel[];
}

interface FormData {
  name: string;
  description: string;
  is_active: boolean;
  has_all_models_access: boolean;
  model_permissions: Array<{
    model_id: string;
    can_access: boolean;
    restrictions: string[];
  }>;
}

export default function Create({ models }: CreateGroupProps) {
  const { data, setData, post, processing, errors } = useForm<FormData>({
    name: '',
    description: '',
    is_active: true,
    has_all_models_access: false,
    model_permissions: [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post('/admin/groups');
  };

  const handleModelToggle = (modelId: string) => {
    const currentPermissions = [...data.model_permissions];
    const existingIndex = currentPermissions.findIndex(p => p.model_id === modelId);
    
    if (existingIndex > -1) {
      currentPermissions.splice(existingIndex, 1);
    } else {
      currentPermissions.push({
        model_id: modelId,
        can_access: true,
        restrictions: [],
      });
    }
    
    setData('model_permissions', currentPermissions);
  };

  const handleAllModelsToggle = (checked: boolean) => {
    setData('has_all_models_access', checked);
    if (checked) {
      setData('model_permissions', []);
    }
  };

  const availableRestrictions = [
    'rate_limit',
    'token_limit',
    'time_based',
    'cost_limit',
  ];

  const handleRestrictionToggle = (modelId: string, restriction: string) => {
    const currentPermissions = [...data.model_permissions];
    const permissionIndex = currentPermissions.findIndex(p => p.model_id === modelId);
    
    if (permissionIndex > -1) {
      const restrictions = [...currentPermissions[permissionIndex].restrictions];
      const restrictionIndex = restrictions.indexOf(restriction);
      
      if (restrictionIndex > -1) {
        restrictions.splice(restrictionIndex, 1);
      } else {
        restrictions.push(restriction);
      }
      
      currentPermissions[permissionIndex].restrictions = restrictions;
      setData('model_permissions', currentPermissions);
    }
  };

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/groups">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Groups
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Create Group</h1>
            <p className="text-muted-foreground">
              Add a new user group with permissions
            </p>
          </div>
        </div>
      }
    >
      <Head title="Create Group" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardHeader>
            <CardTitle>Group Details</CardTitle>
            <CardDescription>
              Configure the basic settings for the new group
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Group Name</Label>
                  <Input
                    id="name"
                    type="text"
                    value={data.name}
                    onChange={(e) => setData('name', e.target.value)}
                    placeholder="Enter group name"
                    required
                  />
                  {errors.name && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.name}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="flex items-center space-x-2 pt-8">
                  <Checkbox
                    id="is_active"
                    checked={data.is_active}
                    onCheckedChange={(checked) => setData('is_active', !!checked)}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={data.description}
                  onChange={(e) => setData('description', e.target.value)}
                  placeholder="Enter group description"
                  rows={3}
                />
                {errors.description && (
                  <Alert variant="destructive">
                    <AlertDescription>{errors.description}</AlertDescription>
                  </Alert>
                )}
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Model Permissions</CardTitle>
                  <CardDescription>
                    Configure which models this group can access
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="has_all_models_access"
                      checked={data.has_all_models_access}
                      onCheckedChange={handleAllModelsToggle}
                    />
                    <Label htmlFor="has_all_models_access">
                      Grant access to all models (including future models)
                    </Label>
                  </div>

                  {!data.has_all_models_access && (
                    <div className="space-y-4">
                      <div className="text-sm font-medium">Select Models:</div>
                      <div className="grid gap-4">
                        {models.map((model) => {
                          const hasAccess = data.model_permissions.some(p => p.model_id === model.id);
                          const permission = data.model_permissions.find(p => p.model_id === model.id);
                          
                          return (
                            <Card key={model.id} className="p-4">
                              <div className="flex items-start space-x-3">
                                <Checkbox
                                  id={model.id}
                                  checked={hasAccess}
                                  onCheckedChange={() => handleModelToggle(model.id)}
                                />
                                <div className="flex-1 space-y-2">
                                  <div className="flex items-center gap-2">
                                    <Label htmlFor={model.id} className="font-medium">
                                      {model.name}
                                    </Label>
                                    <Badge variant="outline" className="text-xs">
                                      {model.provider.name}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    {model.model_identifier}
                                  </p>
                                  
                                  {hasAccess && (
                                    <div className="space-y-2">
                                      <div className="text-sm font-medium">Restrictions:</div>
                                      <div className="flex flex-wrap gap-2">
                                        {availableRestrictions.map((restriction) => (
                                          <div key={restriction} className="flex items-center space-x-1">
                                            <Checkbox
                                              id={`${model.id}-${restriction}`}
                                              checked={permission?.restrictions.includes(restriction) || false}
                                              onCheckedChange={() => handleRestrictionToggle(model.id, restriction)}
                                            />
                                            <Label 
                                              htmlFor={`${model.id}-${restriction}`}
                                              className="text-xs capitalize"
                                            >
                                              {restriction.replace('_', ' ')}
                                            </Label>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </Card>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="flex justify-end gap-4">
                <Button variant="outline" asChild>
                  <Link href="/admin/groups">Cancel</Link>
                </Button>
                <Button type="submit" disabled={processing}>
                  <Users className="h-4 w-4 mr-2" />
                  {processing ? 'Creating...' : 'Create Group'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
