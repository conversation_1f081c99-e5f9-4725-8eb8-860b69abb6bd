import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Users,
  Cpu,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ColumnDef } from '@tanstack/react-table';

interface Group {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  users_count: number;
  llm_models_count: number;
  created_at: string;
  permissions: Record<string, any>;
}

interface GroupsIndexProps {
  groups: Group[];
}

export default function Index({ groups }: GroupsIndexProps) {
  const handleDelete = (group: Group) => {
    if (confirm(`Are you sure you want to delete ${group.name}?`)) {
      router.delete(`/admin/groups/${group.id}`);
    }
  };

  const columns: ColumnDef<Group>[] = [
    {
      accessorKey: 'name',
      header: 'Group',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue('name')}</div>
          {row.original.description && (
            <div className="text-sm text-muted-foreground">{row.original.description}</div>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.getValue('is_active') ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-green-600">Active</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-600 mr-2" />
              <span className="text-red-600">Inactive</span>
            </>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'users_count',
      header: 'Users',
      cell: ({ row }) => (
        <div className="flex items-center">
          <Users className="h-4 w-4 text-muted-foreground mr-2" />
          <span>{row.getValue('users_count')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'llm_models_count',
      header: 'Models',
      cell: ({ row }) => (
        <div className="flex items-center">
          <Cpu className="h-4 w-4 text-muted-foreground mr-2" />
          <span>{row.getValue('llm_models_count')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'permissions',
      header: 'Permissions',
      cell: ({ row }) => {
        const permissions = row.getValue('permissions') as Record<string, any>;
        const permissionCount = Object.keys(permissions || {}).length;
        
        return (
          <div className="flex items-center">
            {permissionCount > 0 ? (
              <Badge variant="outline" className="text-xs">
                {permissionCount} permission{permissionCount !== 1 ? 's' : ''}
              </Badge>
            ) : (
              <span className="text-sm text-muted-foreground">No permissions</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Created',
      cell: ({ row }) => (
        <div className="text-sm">
          {new Date(row.getValue('created_at')).toLocaleDateString()}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const group = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/admin/groups/${group.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/groups/${group.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDelete(group)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <h1 className="text-2xl font-bold">Groups</h1>
            <p className="text-muted-foreground">
              Manage user groups and permissions
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/groups/create">
              <Plus className="h-4 w-4 mr-2" />
              Add Group
            </Link>
          </Button>
        </div>
      }
    >
      <Head title="Groups" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardContent className="p-6">
            <DataTable
              columns={columns}
              data={groups}
              searchPlaceholder="Search groups..."
              searchColumn="name"
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
