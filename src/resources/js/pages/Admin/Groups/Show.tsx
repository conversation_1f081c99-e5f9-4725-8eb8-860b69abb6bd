import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/components/ui/data-table';
import { ArrowLeft, Edit, CheckCircle, XCircle, Users, Cpu, User } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';

interface Provider {
  id: string;
  name: string;
  type: string;
}

interface LlmModel {
  id: string;
  name: string;
  model_identifier: string;
  provider: Provider;
}

interface User {
  id: string;
  name: string;
  email: string;
  email_verified_at: string | null;
}

interface GroupModelPermission {
  id: string;
  can_access: boolean;
  restrictions: any[];
  llm_model: LlmModel;
}

interface Group {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  has_all_models_access: boolean;
  created_at: string;
  updated_at: string;
  users: User[];
  group_model_permissions: GroupModelPermission[];
  stats?: {
    total_requests: number;
    total_tokens: number;
    total_cost: number;
    active_users: number;
  };
}

interface ShowGroupProps {
  group: Group;
}

export default function Show({ group }: ShowGroupProps) {
  const userColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'name',
      header: 'User',
      cell: ({ row }) => (
        <div className="flex items-center">
          <User className="h-4 w-4 text-muted-foreground mr-2" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.email}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'email_verified_at',
      header: 'Status',
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.getValue('email_verified_at') ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-green-600">Verified</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-yellow-600 mr-2" />
              <span className="text-yellow-600">Unverified</span>
            </>
          )}
        </div>
      ),
    },
  ];

  const modelColumns: ColumnDef<GroupModelPermission>[] = [
    {
      accessorKey: 'llm_model.name',
      header: 'Model',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.llm_model.name}</div>
          <div className="text-sm text-muted-foreground">{row.original.llm_model.model_identifier}</div>
        </div>
      ),
    },
    {
      accessorKey: 'llm_model.provider.name',
      header: 'Provider',
      cell: ({ row }) => (
        <Badge variant="outline">{row.original.llm_model.provider.name}</Badge>
      ),
    },
    {
      accessorKey: 'can_access',
      header: 'Access',
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.getValue('can_access') ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-green-600">Allowed</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-600 mr-2" />
              <span className="text-red-600">Denied</span>
            </>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'restrictions',
      header: 'Restrictions',
      cell: ({ row }) => {
        const restrictions = row.getValue('restrictions') as any[];
        return (
          <div className="flex flex-wrap gap-1">
            {restrictions && restrictions.length > 0 ? (
              restrictions.map((restriction, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {restriction.replace('_', ' ')}
                </Badge>
              ))
            ) : (
              <span className="text-sm text-muted-foreground">None</span>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/groups">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Groups
            </Link>
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <Users className="h-6 w-6 text-muted-foreground" />
              <h1 className="text-2xl font-bold">{group.name}</h1>
              {group.is_active ? (
                <Badge variant="default">Active</Badge>
              ) : (
                <Badge variant="destructive">Inactive</Badge>
              )}
            </div>
            <p className="text-muted-foreground">
              Group details and permissions
            </p>
          </div>
          <Button asChild>
            <Link href={`/admin/groups/${group.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Group
            </Link>
          </Button>
        </div>
      }
    >
      <Head title={group.name} />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        {/* Group Information */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Group Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Name</Label>
                <p className="text-sm text-muted-foreground">{group.name}</p>
              </div>
              {group.description && (
                <div>
                  <Label className="text-sm font-medium">Description</Label>
                  <p className="text-sm text-muted-foreground">{group.description}</p>
                </div>
              )}
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="flex items-center mt-1">
                  {group.is_active ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-green-600">Active</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 text-red-600 mr-2" />
                      <span className="text-red-600">Inactive</span>
                    </>
                  )}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Model Access</Label>
                <p className="text-sm text-muted-foreground">
                  {group.has_all_models_access ? 'All models' : `${group.group_model_permissions.length} specific models`}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Created</Label>
                <p className="text-sm text-muted-foreground">
                  {new Date(group.created_at).toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          {group.stats && (
            <Card>
              <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Total Requests</Label>
                  <p className="text-2xl font-bold">{group.stats.total_requests.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Tokens</Label>
                  <p className="text-2xl font-bold">{group.stats.total_tokens.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Cost</Label>
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'USD',
                    }).format(group.stats.total_cost)}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Active Users</Label>
                  <p className="text-2xl font-bold">{group.stats.active_users}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Users */}
        <Card>
          <CardHeader>
            <CardTitle>Users ({group.users.length})</CardTitle>
            <CardDescription>
              Users assigned to this group
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              columns={userColumns}
              data={group.users}
              searchPlaceholder="Search users..."
              searchColumn="name"
            />
          </CardContent>
        </Card>

        {/* Model Permissions */}
        <Card>
          <CardHeader>
            <CardTitle>Model Permissions</CardTitle>
            <CardDescription>
              {group.has_all_models_access 
                ? 'This group has access to all models'
                : `Specific model permissions for this group`
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {group.has_all_models_access ? (
              <div className="text-center py-8">
                <Cpu className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-lg font-medium">All Models Access</p>
                <p className="text-sm text-muted-foreground">
                  This group has access to all current and future models
                </p>
              </div>
            ) : (
              <DataTable
                columns={modelColumns}
                data={group.group_model_permissions}
                searchPlaceholder="Search models..."
                searchColumn="llm_model.name"
              />
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

function Label({ className, children, ...props }: React.LabelHTMLAttributes<HTMLLabelElement>) {
  return (
    <label className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className}`} {...props}>
      {children}
    </label>
  );
}
