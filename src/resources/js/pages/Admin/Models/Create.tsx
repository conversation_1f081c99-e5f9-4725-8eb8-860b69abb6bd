import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface Provider {
  id: string;
  name: string;
  type: string;
  base_url: string;
}

interface CreateModelProps {
  providers: Provider[];
}

interface FormData {
  provider_id: string;
  name: string;
  model_identifier: string;
  is_active: boolean;
  max_tokens: string;
  cost_per_input_token: string;
  cost_per_output_token: string;
  capabilities: string[];
}

export default function Create({ providers }: CreateModelProps) {
  const { data, setData, post, processing, errors } = useForm<FormData>({
    provider_id: '',
    name: '',
    model_identifier: '',
    is_active: true,
    max_tokens: '',
    cost_per_input_token: '',
    cost_per_output_token: '',
    capabilities: [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post('/admin/models');
  };

  const handleCapabilityToggle = (capability: string) => {
    const currentCapabilities = [...data.capabilities];
    const index = currentCapabilities.indexOf(capability);
    
    if (index > -1) {
      currentCapabilities.splice(index, 1);
    } else {
      currentCapabilities.push(capability);
    }
    
    setData('capabilities', currentCapabilities);
  };

  const availableCapabilities = [
    'text-generation',
    'chat',
    'function-calling',
    'vision',
    'code-generation',
    'reasoning',
    'multimodal',
  ];

  const breadcrumbItems = [
    { name: 'Models', href: '/admin/models' },
    { name: 'Create', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center space-x-4">
          <Link
            href="/admin/models"
            className="text-gray-400 hover:text-gray-600"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </Link>
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Create Model
            </h1>
          </div>
        </div>
      }
    >
      <Head title="Create Model" />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Provider Selection */}
            <div>
              <label htmlFor="provider_id" className="block text-sm font-medium text-gray-700">
                Provider
              </label>
              <select
                id="provider_id"
                value={data.provider_id}
                onChange={(e) => setData('provider_id', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                required
              >
                <option value="">Select a provider</option>
                {providers.map((provider) => (
                  <option key={provider.id} value={provider.id}>
                    {provider.name} ({provider.type})
                  </option>
                ))}
              </select>
              {errors.provider_id && (
                <p className="mt-1 text-sm text-red-600">{errors.provider_id}</p>
              )}
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Display Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={data.name}
                  onChange={(e) => setData('name', e.target.value)}
                  placeholder="e.g., GPT-4 Turbo"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="model_identifier" className="block text-sm font-medium text-gray-700">
                  Model Identifier
                </label>
                <input
                  type="text"
                  id="model_identifier"
                  value={data.model_identifier}
                  onChange={(e) => setData('model_identifier', e.target.value)}
                  placeholder="e.g., gpt-4-turbo-preview"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p className="mt-1 text-sm text-gray-500">
                  The exact model identifier used in API calls.
                </p>
                {errors.model_identifier && (
                  <p className="mt-1 text-sm text-red-600">{errors.model_identifier}</p>
                )}
              </div>
            </div>

            {/* Configuration */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
              <div>
                <label htmlFor="max_tokens" className="block text-sm font-medium text-gray-700">
                  Max Tokens
                </label>
                <input
                  type="number"
                  id="max_tokens"
                  value={data.max_tokens}
                  onChange={(e) => setData('max_tokens', e.target.value)}
                  placeholder="e.g., 4096"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
                {errors.max_tokens && (
                  <p className="mt-1 text-sm text-red-600">{errors.max_tokens}</p>
                )}
              </div>

              <div>
                <label htmlFor="cost_per_input_token" className="block text-sm font-medium text-gray-700">
                  Cost per Input Token ($)
                </label>
                <input
                  type="number"
                  step="0.000001"
                  id="cost_per_input_token"
                  value={data.cost_per_input_token}
                  onChange={(e) => setData('cost_per_input_token', e.target.value)}
                  placeholder="e.g., 0.00001"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
                {errors.cost_per_input_token && (
                  <p className="mt-1 text-sm text-red-600">{errors.cost_per_input_token}</p>
                )}
              </div>

              <div>
                <label htmlFor="cost_per_output_token" className="block text-sm font-medium text-gray-700">
                  Cost per Output Token ($)
                </label>
                <input
                  type="number"
                  step="0.000001"
                  id="cost_per_output_token"
                  value={data.cost_per_output_token}
                  onChange={(e) => setData('cost_per_output_token', e.target.value)}
                  placeholder="e.g., 0.00003"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
                {errors.cost_per_output_token && (
                  <p className="mt-1 text-sm text-red-600">{errors.cost_per_output_token}</p>
                )}
              </div>
            </div>

            {/* Capabilities */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Capabilities
              </label>
              <div className="grid grid-cols-2 gap-3 sm:grid-cols-4">
                {availableCapabilities.map((capability) => (
                  <div key={capability} className="flex items-center">
                    <input
                      id={`capability-${capability}`}
                      type="checkbox"
                      checked={data.capabilities.includes(capability)}
                      onChange={() => handleCapabilityToggle(capability)}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label htmlFor={`capability-${capability}`} className="ml-3 block text-sm text-gray-700">
                      {capability}
                    </label>
                  </div>
                ))}
              </div>
              {errors.capabilities && (
                <p className="mt-1 text-sm text-red-600">{errors.capabilities}</p>
              )}
            </div>

            {/* Status */}
            <div>
              <div className="flex items-center">
                <input
                  id="is_active"
                  type="checkbox"
                  checked={data.is_active}
                  onChange={(e) => setData('is_active', e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-3 block text-sm text-gray-700">
                  Active
                </label>
              </div>
              {errors.is_active && (
                <p className="mt-1 text-sm text-red-600">{errors.is_active}</p>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3">
              <Link
                href="/admin/models"
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={processing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {processing ? 'Creating...' : 'Create Model'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
