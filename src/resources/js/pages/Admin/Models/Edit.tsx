import React from 'react';
import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Save, Trash2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Provider {
  id: string;
  name: string;
  type: string;
  base_url: string;
}

interface LlmModel {
  id: string;
  provider_id: string;
  name: string;
  model_identifier: string;
  is_active: boolean;
  max_tokens: number;
  cost_per_input_token: number;
  cost_per_output_token: number;
  capabilities: string[];
  created_at: string;
  updated_at: string;
}

interface EditModelProps {
  model: LlmModel;
  providers: Provider[];
}

interface FormData {
  provider_id: string;
  name: string;
  model_identifier: string;
  is_active: boolean;
  max_tokens: string;
  cost_per_input_token: string;
  cost_per_output_token: string;
  capabilities: string[];
}

export default function Edit({ model, providers }: EditModelProps) {
  const { data, setData, put, processing, errors } = useForm<FormData>({
    provider_id: model.provider_id,
    name: model.name,
    model_identifier: model.model_identifier,
    is_active: model.is_active,
    max_tokens: model.max_tokens.toString(),
    cost_per_input_token: model.cost_per_input_token.toString(),
    cost_per_output_token: model.cost_per_output_token.toString(),
    capabilities: model.capabilities || [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(`/admin/models/${model.id}`);
  };

  const handleCapabilityToggle = (capability: string) => {
    const currentCapabilities = [...data.capabilities];
    const index = currentCapabilities.indexOf(capability);
    
    if (index > -1) {
      currentCapabilities.splice(index, 1);
    } else {
      currentCapabilities.push(capability);
    }
    
    setData('capabilities', currentCapabilities);
  };

  const availableCapabilities = [
    'text-generation',
    'code-generation',
    'function-calling',
    'vision',
    'embeddings',
    'fine-tuning',
    'streaming',
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/models">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Models
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edit Model</h1>
            <p className="text-muted-foreground">
              Update model configuration and settings
            </p>
          </div>
        </div>
      }
    >
      <Head title={`Edit ${model.name}`} />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardHeader>
            <CardTitle>Model Details</CardTitle>
            <CardDescription>
              Update the configuration for {model.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="provider_id">Provider</Label>
                  <Select value={data.provider_id} onValueChange={(value) => setData('provider_id', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a provider" />
                    </SelectTrigger>
                    <SelectContent>
                      {providers.map((provider) => (
                        <SelectItem key={provider.id} value={provider.id}>
                          {provider.name} ({provider.type})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.provider_id && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.provider_id}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">Model Name</Label>
                  <Input
                    id="name"
                    type="text"
                    value={data.name}
                    onChange={(e) => setData('name', e.target.value)}
                    placeholder="Enter model name"
                    required
                  />
                  {errors.name && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.name}</AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="model_identifier">Model Identifier</Label>
                <Input
                  id="model_identifier"
                  type="text"
                  value={data.model_identifier}
                  onChange={(e) => setData('model_identifier', e.target.value)}
                  placeholder="e.g., gpt-4, claude-3-opus"
                  required
                />
                {errors.model_identifier && (
                  <Alert variant="destructive">
                    <AlertDescription>{errors.model_identifier}</AlertDescription>
                  </Alert>
                )}
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="max_tokens">Max Tokens</Label>
                  <Input
                    id="max_tokens"
                    type="number"
                    value={data.max_tokens}
                    onChange={(e) => setData('max_tokens', e.target.value)}
                    placeholder="4096"
                    required
                  />
                  {errors.max_tokens && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.max_tokens}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cost_per_input_token">Cost per Input Token</Label>
                  <Input
                    id="cost_per_input_token"
                    type="number"
                    step="0.000001"
                    value={data.cost_per_input_token}
                    onChange={(e) => setData('cost_per_input_token', e.target.value)}
                    placeholder="0.000010"
                    required
                  />
                  {errors.cost_per_input_token && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.cost_per_input_token}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cost_per_output_token">Cost per Output Token</Label>
                  <Input
                    id="cost_per_output_token"
                    type="number"
                    step="0.000001"
                    value={data.cost_per_output_token}
                    onChange={(e) => setData('cost_per_output_token', e.target.value)}
                    placeholder="0.000030"
                    required
                  />
                  {errors.cost_per_output_token && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.cost_per_output_token}</AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  checked={data.is_active}
                  onCheckedChange={(checked) => setData('is_active', !!checked)}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Capabilities</CardTitle>
                  <CardDescription>
                    Select the capabilities this model supports
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2">
                    {availableCapabilities.map((capability) => (
                      <div key={capability} className="flex items-center space-x-2">
                        <Checkbox
                          id={capability}
                          checked={data.capabilities.includes(capability)}
                          onCheckedChange={() => handleCapabilityToggle(capability)}
                        />
                        <Label htmlFor={capability} className="capitalize">
                          {capability.replace('-', ' ')}
                        </Label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="destructive" asChild>
                  <Link href={`/admin/models/${model.id}`} method="delete" as="button">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Model
                  </Link>
                </Button>
                
                <div className="flex gap-4">
                  <Button variant="outline" asChild>
                    <Link href="/admin/models">Cancel</Link>
                  </Button>
                  <Button type="submit" disabled={processing}>
                    <Save className="h-4 w-4 mr-2" />
                    {processing ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
