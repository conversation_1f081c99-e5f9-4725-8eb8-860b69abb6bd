import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  MoreHorizontal,
  DollarSign
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ColumnDef } from '@tanstack/react-table';

interface LlmModel {
  id: string;
  name: string;
  model_identifier: string;
  is_active: boolean;
  max_tokens: number;
  cost_per_input_token: number;
  cost_per_output_token: number;
  capabilities: string[];
  created_at: string;
  provider: {
    id: string;
    name: string;
    type: string;
  };
}

interface ModelsIndexProps {
  models: LlmModel[];
}

export default function Index({ models }: ModelsIndexProps) {
  const handleDelete = (model: LlmModel) => {
    if (confirm(`Are you sure you want to delete ${model.name}?`)) {
      router.delete(`/admin/models/${model.id}`);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 6,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  const columns: ColumnDef<LlmModel>[] = [
    {
      accessorKey: 'name',
      header: 'Model',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue('name')}</div>
          <div className="text-sm text-muted-foreground">{row.original.model_identifier}</div>
        </div>
      ),
    },
    {
      accessorKey: 'provider',
      header: 'Provider',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.provider.name}</div>
          <div className="text-sm text-muted-foreground capitalize">
            {row.original.provider.type.replace('_', ' ')}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.getValue('is_active') ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-green-600">Active</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-600 mr-2" />
              <span className="text-red-600">Inactive</span>
            </>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'max_tokens',
      header: 'Max Tokens',
      cell: ({ row }) => (
        <div className="text-sm">
          {(row.getValue('max_tokens') as number).toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: 'cost_per_input_token',
      header: 'Input Cost',
      cell: ({ row }) => (
        <div className="text-sm font-mono">
          {formatCurrency(row.getValue('cost_per_input_token'))}
        </div>
      ),
    },
    {
      accessorKey: 'cost_per_output_token',
      header: 'Output Cost',
      cell: ({ row }) => (
        <div className="text-sm font-mono">
          {formatCurrency(row.getValue('cost_per_output_token'))}
        </div>
      ),
    },
    {
      accessorKey: 'capabilities',
      header: 'Capabilities',
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.capabilities.slice(0, 2).map((capability) => (
            <Badge key={capability} variant="outline" className="text-xs">
              {capability}
            </Badge>
          ))}
          {row.original.capabilities.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{row.original.capabilities.length - 2} more
            </Badge>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'created_at',
      header: 'Created',
      cell: ({ row }) => (
        <div className="text-sm">
          {new Date(row.getValue('created_at')).toLocaleDateString()}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const model = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/admin/models/${model.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/models/${model.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDelete(model)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <h1 className="text-2xl font-bold">Models</h1>
            <p className="text-muted-foreground">
              Configure models, pricing, and capabilities
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/models/create">
              <Plus className="h-4 w-4 mr-2" />
              Add Model
            </Link>
          </Button>
        </div>
      }
    >
      <Head title="Models" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardContent className="p-6">
            <DataTable
              columns={columns}
              data={models}
              searchPlaceholder="Search models..."
              searchColumn="name"
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
