import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Edit, CheckCircle, XCircle, Cpu, DollarSign } from 'lucide-react';

interface Provider {
  id: string;
  name: string;
  type: string;
  base_url: string;
}

interface LlmModel {
  id: string;
  provider_id: string;
  name: string;
  model_identifier: string;
  is_active: boolean;
  max_tokens: number;
  cost_per_input_token: number;
  cost_per_output_token: number;
  capabilities: string[];
  created_at: string;
  updated_at: string;
  provider: Provider;
  stats?: {
    total_requests: number;
    total_tokens: number;
    total_cost: number;
    avg_duration: number;
  };
}

interface ShowModelProps {
  model: LlmModel;
}

export default function Show({ model }: ShowModelProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 6,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  const formatCurrencyShort = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/models">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Models
            </Link>
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <Cpu className="h-6 w-6 text-muted-foreground" />
              <h1 className="text-2xl font-bold">{model.name}</h1>
              {model.is_active ? (
                <Badge variant="default">Active</Badge>
              ) : (
                <Badge variant="destructive">Inactive</Badge>
              )}
            </div>
            <p className="text-muted-foreground">
              Model details and configuration
            </p>
          </div>
          <Button asChild>
            <Link href={`/admin/models/${model.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Model
            </Link>
          </Button>
        </div>
      }
    >
      <Head title={model.name} />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        {/* Model Information */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Model Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Name</Label>
                <p className="text-sm text-muted-foreground">{model.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Model Identifier</Label>
                <p className="text-sm text-muted-foreground font-mono">{model.model_identifier}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Provider</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline">{model.provider.name}</Badge>
                  <span className="text-sm text-muted-foreground capitalize">
                    ({model.provider.type.replace('_', ' ')})
                  </span>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="flex items-center mt-1">
                  {model.is_active ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-green-600">Active</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 text-red-600 mr-2" />
                      <span className="text-red-600">Inactive</span>
                    </>
                  )}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Max Tokens</Label>
                <p className="text-sm text-muted-foreground">
                  {model.max_tokens.toLocaleString()}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Created</Label>
                <p className="text-sm text-muted-foreground">
                  {new Date(model.created_at).toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Input Token Cost</Label>
                <div className="flex items-center mt-1">
                  <DollarSign className="h-4 w-4 text-muted-foreground mr-2" />
                  <span className="text-sm font-mono">{formatCurrency(model.cost_per_input_token)}</span>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Output Token Cost</Label>
                <div className="flex items-center mt-1">
                  <DollarSign className="h-4 w-4 text-muted-foreground mr-2" />
                  <span className="text-sm font-mono">{formatCurrency(model.cost_per_output_token)}</span>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Cost Ratio</Label>
                <p className="text-sm text-muted-foreground">
                  Output costs {(model.cost_per_output_token / model.cost_per_input_token).toFixed(1)}x more than input
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Statistics */}
        {model.stats && (
          <Card>
            <CardHeader>
              <CardTitle>Usage Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div>
                  <Label className="text-sm font-medium">Total Requests</Label>
                  <p className="text-2xl font-bold">{model.stats.total_requests.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Tokens</Label>
                  <p className="text-2xl font-bold">{model.stats.total_tokens.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Cost</Label>
                  <p className="text-2xl font-bold">{formatCurrencyShort(model.stats.total_cost)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Avg Duration</Label>
                  <p className="text-2xl font-bold">{model.stats.avg_duration.toFixed(2)}s</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Capabilities */}
        <Card>
          <CardHeader>
            <CardTitle>Capabilities</CardTitle>
            <CardDescription>
              Features and capabilities supported by this model
            </CardDescription>
          </CardHeader>
          <CardContent>
            {model.capabilities && model.capabilities.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {model.capabilities.map((capability) => (
                  <Badge key={capability} variant="outline" className="capitalize">
                    {capability.replace('-', ' ')}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No capabilities defined</p>
            )}
          </CardContent>
        </Card>

        {/* Provider Details */}
        <Card>
          <CardHeader>
            <CardTitle>Provider Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Provider Name</Label>
              <p className="text-sm text-muted-foreground">{model.provider.name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Provider Type</Label>
              <Badge variant="outline" className="capitalize">
                {model.provider.type.replace('_', ' ')}
              </Badge>
            </div>
            <div>
              <Label className="text-sm font-medium">Base URL</Label>
              <p className="text-sm text-muted-foreground font-mono">{model.provider.base_url}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

function Label({ className, children, ...props }: React.LabelHTMLAttributes<HTMLLabelElement>) {
  return (
    <label className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className}`} {...props}>
      {children}
    </label>
  );
}
