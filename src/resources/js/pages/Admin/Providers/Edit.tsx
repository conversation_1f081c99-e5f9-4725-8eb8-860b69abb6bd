import React from 'react';
import { <PERSON>, useF<PERSON>, Link } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Save, Trash2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Provider {
  id: string;
  name: string;
  type: 'anthropic' | 'openai' | 'openai_compatible';
  base_url: string;
  api_key: string;
  is_active: boolean;
  config: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface EditProviderProps {
  provider: Provider;
}

export default function Edit({ provider }: EditProviderProps) {
  const { data, setData, put, processing, errors } = useForm({
    name: provider.name,
    type: provider.type,
    base_url: provider.base_url,
    api_key: provider.api_key,
    is_active: provider.is_active,
    config: provider.config || {},
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(`/admin/providers/${provider.id}`);
  };

  const handleTypeChange = (type: 'anthropic' | 'openai' | 'openai_compatible') => {
    setData(prev => ({
      ...prev,
      type,
      base_url: getDefaultBaseUrl(type),
      config: getDefaultConfig(type),
    }));
  };

  const getDefaultBaseUrl = (type: string) => {
    switch (type) {
      case 'anthropic':
        return 'https://api.anthropic.com';
      case 'openai':
        return 'https://api.openai.com';
      case 'openai_compatible':
        return '';
      default:
        return '';
    }
  };

  const getDefaultConfig = (type: string) => {
    switch (type) {
      case 'anthropic':
        return {
          version: '2023-06-01',
          max_tokens: 4096,
          ...data.config,
        };
      case 'openai':
        return {
          organization: '',
          max_tokens: 4096,
          ...data.config,
        };
      case 'openai_compatible':
        return {
          max_tokens: 4096,
          ...data.config,
        };
      default:
        return data.config;
    }
  };

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/providers">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Providers
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edit Provider</h1>
            <p className="text-muted-foreground">
              Update provider configuration and settings
            </p>
          </div>
        </div>
      }
    >
      <Head title={`Edit ${provider.name}`} />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardHeader>
            <CardTitle>Provider Details</CardTitle>
            <CardDescription>
              Update the configuration for {provider.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Provider Name</Label>
                  <Input
                    id="name"
                    type="text"
                    value={data.name}
                    onChange={(e) => setData('name', e.target.value)}
                    placeholder="Enter provider name"
                    required
                  />
                  {errors.name && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.name}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">Provider Type</Label>
                  <Select value={data.type} onValueChange={handleTypeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select provider type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI</SelectItem>
                      <SelectItem value="anthropic">Anthropic</SelectItem>
                      <SelectItem value="openai_compatible">OpenAI Compatible</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.type && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.type}</AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="base_url">Base URL</Label>
                <Input
                  id="base_url"
                  type="url"
                  value={data.base_url}
                  onChange={(e) => setData('base_url', e.target.value)}
                  placeholder="https://api.example.com"
                  required
                />
                {errors.base_url && (
                  <Alert variant="destructive">
                    <AlertDescription>{errors.base_url}</AlertDescription>
                  </Alert>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="api_key">API Key</Label>
                <Input
                  id="api_key"
                  type="password"
                  value={data.api_key}
                  onChange={(e) => setData('api_key', e.target.value)}
                  placeholder="Enter your API key"
                  required
                />
                {errors.api_key && (
                  <Alert variant="destructive">
                    <AlertDescription>{errors.api_key}</AlertDescription>
                  </Alert>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  checked={data.is_active}
                  onCheckedChange={(checked) => setData('is_active', !!checked)}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>

              {/* Provider-specific configuration */}
              {data.type === 'anthropic' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Anthropic Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="version">API Version</Label>
                      <Input
                        id="version"
                        type="text"
                        value={data.config.version || '2023-06-01'}
                        onChange={(e) => setData('config', { ...data.config, version: e.target.value })}
                        placeholder="2023-06-01"
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {data.type === 'openai' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">OpenAI Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="organization">Organization ID (Optional)</Label>
                      <Input
                        id="organization"
                        type="text"
                        value={data.config.organization || ''}
                        onChange={(e) => setData('config', { ...data.config, organization: e.target.value })}
                        placeholder="org-xxxxxxxxxx"
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="flex justify-between">
                <Button variant="destructive" asChild>
                  <Link href={`/admin/providers/${provider.id}`} method="delete" as="button">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Provider
                  </Link>
                </Button>
                
                <div className="flex gap-4">
                  <Button variant="outline" asChild>
                    <Link href="/admin/providers">Cancel</Link>
                  </Button>
                  <Button type="submit" disabled={processing}>
                    <Save className="h-4 w-4 mr-2" />
                    {processing ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
