import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Play,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ColumnDef } from '@tanstack/react-table';

interface Provider {
  id: string;
  name: string;
  type: 'anthropic' | 'openai' | 'openai_compatible';
  base_url: string;
  is_active: boolean;
  created_at: string;
  llm_models: Array<{
    id: string;
    name: string;
    is_active: boolean;
  }>;
}

interface ProvidersIndexProps {
  providers: Provider[];
}

export default function Index({ providers }: ProvidersIndexProps) {
  const handleDelete = (provider: Provider) => {
    if (confirm(`Are you sure you want to delete ${provider.name}?`)) {
      router.delete(`/admin/providers/${provider.id}`);
    }
  };

  const handleTestConnection = async (provider: Provider) => {
    try {
      const response = await fetch(`/admin/providers/${provider.id}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': (document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement)?.content || '',
        },
      });

      const result = await response.json();

      if (result.success) {
        alert('Connection test successful!');
      } else {
        alert('Connection test failed: ' + result.message);
      }
    } catch (error) {
      alert('Connection test failed: ' + error);
    }
  };

  const getProviderTypeBadge = (type: string) => {
    const variants = {
      anthropic: 'secondary',
      openai: 'default',
      openai_compatible: 'outline',
    } as const;
    
    return (
      <Badge variant={variants[type as keyof typeof variants] || 'default'}>
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const columns: ColumnDef<Provider>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="flex items-center">
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.base_url}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => getProviderTypeBadge(row.getValue('type')),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.getValue('is_active') ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-green-600">Active</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-600 mr-2" />
              <span className="text-red-600">Inactive</span>
            </>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'llm_models',
      header: 'Models',
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.llm_models.slice(0, 3).map((model) => (
            <Badge key={model.id} variant="outline" className="text-xs">
              {model.name}
            </Badge>
          ))}
          {row.original.llm_models.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{row.original.llm_models.length - 3} more
            </Badge>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'created_at',
      header: 'Created',
      cell: ({ row }) => (
        <div className="text-sm">
          {new Date(row.getValue('created_at')).toLocaleDateString()}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const provider = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/admin/providers/${provider.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/providers/${provider.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleTestConnection(provider)}>
                <Play className="mr-2 h-4 w-4" />
                Test Connection
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDelete(provider)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <h1 className="text-2xl font-bold">Providers</h1>
            <p className="text-muted-foreground">
              Manage your LLM providers and API configurations
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/providers/create">
              <Plus className="h-4 w-4 mr-2" />
              Add Provider
            </Link>
          </Button>
        </div>
      }
    >
      <Head title="Providers" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardContent className="p-6">
            <DataTable
              columns={columns}
              data={providers}
              searchPlaceholder="Search providers..."
              searchColumn="name"
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
