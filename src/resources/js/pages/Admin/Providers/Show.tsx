import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/components/ui/data-table';
import { ArrowLeft, Edit, CheckCircle, XCircle, Play, Server } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';

interface Provider {
  id: string;
  name: string;
  type: 'anthropic' | 'openai' | 'openai_compatible';
  base_url: string;
  is_active: boolean;
  config: Record<string, any>;
  created_at: string;
  updated_at: string;
  llm_models: Array<{
    id: string;
    name: string;
    model_identifier: string;
    is_active: boolean;
    max_tokens: number;
    cost_per_input_token: number;
    cost_per_output_token: number;
  }>;
  stats?: {
    total_requests: number;
    total_tokens: number;
    total_cost: number;
    success_rate: number;
  };
}

interface ShowProviderProps {
  provider: Provider;
}

export default function Show({ provider }: ShowProviderProps) {
  const getProviderTypeBadge = (type: string) => {
    const variants = {
      anthropic: 'secondary',
      openai: 'default',
      openai_compatible: 'outline',
    } as const;
    
    return (
      <Badge variant={variants[type as keyof typeof variants] || 'default'}>
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 6,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  const modelColumns: ColumnDef<any>[] = [
    {
      accessorKey: 'name',
      header: 'Model',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue('name')}</div>
          <div className="text-sm text-muted-foreground">{row.original.model_identifier}</div>
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.getValue('is_active') ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-green-600">Active</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-600 mr-2" />
              <span className="text-red-600">Inactive</span>
            </>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'max_tokens',
      header: 'Max Tokens',
      cell: ({ row }) => (
        <div className="text-sm">
          {(row.getValue('max_tokens') as number).toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: 'cost_per_input_token',
      header: 'Input Cost',
      cell: ({ row }) => (
        <div className="text-sm font-mono">
          {formatCurrency(row.getValue('cost_per_input_token'))}
        </div>
      ),
    },
    {
      accessorKey: 'cost_per_output_token',
      header: 'Output Cost',
      cell: ({ row }) => (
        <div className="text-sm font-mono">
          {formatCurrency(row.getValue('cost_per_output_token'))}
        </div>
      ),
    },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/providers">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Providers
            </Link>
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <Server className="h-6 w-6 text-muted-foreground" />
              <h1 className="text-2xl font-bold">{provider.name}</h1>
              {getProviderTypeBadge(provider.type)}
              {provider.is_active ? (
                <Badge variant="default">Active</Badge>
              ) : (
                <Badge variant="destructive">Inactive</Badge>
              )}
            </div>
            <p className="text-muted-foreground">
              Provider details and configuration
            </p>
          </div>
          <Button asChild>
            <Link href={`/admin/providers/${provider.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Provider
            </Link>
          </Button>
        </div>
      }
    >
      <Head title={provider.name} />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        {/* Provider Information */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Provider Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Name</Label>
                <p className="text-sm text-muted-foreground">{provider.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Type</Label>
                <div className="mt-1">{getProviderTypeBadge(provider.type)}</div>
              </div>
              <div>
                <Label className="text-sm font-medium">Base URL</Label>
                <p className="text-sm text-muted-foreground font-mono">{provider.base_url}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="flex items-center mt-1">
                  {provider.is_active ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-green-600">Active</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 text-red-600 mr-2" />
                      <span className="text-red-600">Inactive</span>
                    </>
                  )}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Created</Label>
                <p className="text-sm text-muted-foreground">
                  {new Date(provider.created_at).toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          {provider.stats && (
            <Card>
              <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Total Requests</Label>
                  <p className="text-2xl font-bold">{provider.stats.total_requests.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Tokens</Label>
                  <p className="text-2xl font-bold">{provider.stats.total_tokens.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Cost</Label>
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'USD',
                    }).format(provider.stats.total_cost)}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Success Rate</Label>
                  <p className="text-2xl font-bold">{provider.stats.success_rate.toFixed(1)}%</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Configuration */}
        {Object.keys(provider.config || {}).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-sm bg-muted p-4 rounded-md overflow-auto">
                {JSON.stringify(provider.config, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* Models */}
        <Card>
          <CardHeader>
            <CardTitle>Models ({provider.llm_models.length})</CardTitle>
            <CardDescription>
              Models configured for this provider
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              columns={modelColumns}
              data={provider.llm_models}
              searchPlaceholder="Search models..."
              searchColumn="name"
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

function Label({ className, children, ...props }: React.LabelHTMLAttributes<HTMLLabelElement>) {
  return (
    <label className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className}`} {...props}>
      {children}
    </label>
  );
}
