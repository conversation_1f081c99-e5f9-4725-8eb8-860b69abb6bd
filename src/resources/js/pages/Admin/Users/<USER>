import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Save, Trash2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Group {
  id: string;
  name: string;
  description?: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  email_verified_at: string | null;
  created_at: string;
  groups: Array<{
    id: string;
    name: string;
  }>;
}

interface EditUserProps {
  user: User;
  groups: Group[];
}

interface FormData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  groups: string[];
}

export default function Edit({ user, groups }: EditUserProps) {
  const { data, setData, put, processing, errors } = useForm<FormData>({
    name: user.name,
    email: user.email,
    password: '',
    password_confirmation: '',
    groups: user.groups.map(group => group.id),
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(`/admin/users/${user.id}`);
  };

  const handleGroupToggle = (groupId: string) => {
    const currentGroups = [...data.groups];
    const index = currentGroups.indexOf(groupId);
    
    if (index > -1) {
      currentGroups.splice(index, 1);
    } else {
      currentGroups.push(groupId);
    }
    
    setData('groups', currentGroups);
  };

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/users">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edit User</h1>
            <p className="text-muted-foreground">
              Update user information and group assignments
            </p>
          </div>
        </div>
      }
    >
      <Head title={`Edit ${user.name}`} />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardHeader>
            <CardTitle>User Details</CardTitle>
            <CardDescription>
              Update the configuration for {user.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    type="text"
                    value={data.name}
                    onChange={(e) => setData('name', e.target.value)}
                    placeholder="Enter full name"
                    required
                  />
                  {errors.name && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.name}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={data.email}
                    onChange={(e) => setData('email', e.target.value)}
                    placeholder="Enter email address"
                    required
                  />
                  {errors.email && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.email}</AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Change Password</CardTitle>
                  <CardDescription>
                    Leave blank to keep current password
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="password">New Password</Label>
                      <Input
                        id="password"
                        type="password"
                        value={data.password}
                        onChange={(e) => setData('password', e.target.value)}
                        placeholder="Enter new password"
                      />
                      {errors.password && (
                        <Alert variant="destructive">
                          <AlertDescription>{errors.password}</AlertDescription>
                        </Alert>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password_confirmation">Confirm New Password</Label>
                      <Input
                        id="password_confirmation"
                        type="password"
                        value={data.password_confirmation}
                        onChange={(e) => setData('password_confirmation', e.target.value)}
                        placeholder="Confirm new password"
                      />
                      {errors.password_confirmation && (
                        <Alert variant="destructive">
                          <AlertDescription>{errors.password_confirmation}</AlertDescription>
                        </Alert>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {groups.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Group Assignments</CardTitle>
                    <CardDescription>
                      Select which groups this user should belong to
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-3 md:grid-cols-2">
                      {groups.map((group) => (
                        <div key={group.id} className="flex items-start space-x-2">
                          <Checkbox
                            id={group.id}
                            checked={data.groups.includes(group.id)}
                            onCheckedChange={() => handleGroupToggle(group.id)}
                          />
                          <div className="grid gap-1.5 leading-none">
                            <Label htmlFor={group.id} className="font-medium">
                              {group.name}
                            </Label>
                            {group.description && (
                              <p className="text-xs text-muted-foreground">
                                {group.description}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    {errors.groups && (
                      <Alert variant="destructive" className="mt-4">
                        <AlertDescription>{errors.groups}</AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              )}

              <div className="flex justify-between">
                <Button variant="destructive" asChild>
                  <Link href={`/admin/users/${user.id}`} method="delete" as="button">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete User
                  </Link>
                </Button>
                
                <div className="flex gap-4">
                  <Button variant="outline" asChild>
                    <Link href="/admin/users">Cancel</Link>
                  </Button>
                  <Button type="submit" disabled={processing}>
                    <Save className="h-4 w-4 mr-2" />
                    {processing ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
