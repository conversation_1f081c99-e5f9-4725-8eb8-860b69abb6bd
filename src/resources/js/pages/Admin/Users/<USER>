import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Key,
  Users,
  MoreHorizontal,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ColumnDef } from '@tanstack/react-table';

interface User {
  id: string;
  name: string;
  email: string;
  email_verified_at: string | null;
  created_at: string;
  groups: Array<{
    id: string;
    name: string;
  }>;
  api_keys_count: number;
  request_logs_count: number;
}

interface UsersIndexProps {
  users: User[];
}

export default function UsersIndex({ users }: UsersIndexProps) {
  const handleDelete = (user: User) => {
    if (confirm(`Are you sure you want to delete user "${user.name}"?`)) {
      router.delete(`/admin/users/${user.id}`, {
        onSuccess: () => {
          // Handle success if needed
        },
        onError: (errors) => {
          console.error('Delete failed:', errors);
        },
      });
    }
  };

  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="flex items-center">
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.email}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'email_verified_at',
      header: 'Status',
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.getValue('email_verified_at') ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-green-600">Verified</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-yellow-600 mr-2" />
              <span className="text-yellow-600">Unverified</span>
            </>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'groups',
      header: 'Groups',
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.groups.slice(0, 2).map((group) => (
            <Badge key={group.id} variant="outline" className="text-xs">
              {group.name}
            </Badge>
          ))}
          {row.original.groups.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{row.original.groups.length - 2} more
            </Badge>
          )}
          {row.original.groups.length === 0 && (
            <span className="text-sm text-muted-foreground">No groups</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'api_keys_count',
      header: 'API Keys',
      cell: ({ row }) => (
        <div className="flex items-center">
          <Key className="h-4 w-4 text-muted-foreground mr-2" />
          <span>{row.getValue('api_keys_count')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'request_logs_count',
      header: 'Requests',
      cell: ({ row }) => (
        <div className="text-sm">
          {(row.getValue('request_logs_count') as number).toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: 'created_at',
      header: 'Created',
      cell: ({ row }) => (
        <div className="text-sm">
          {new Date(row.getValue('created_at')).toLocaleDateString()}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const user = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/admin/users/${user.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/users/${user.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/users/${user.id}/api-keys`}>
                  <Key className="mr-2 h-4 w-4" />
                  API Keys
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDelete(user)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <h1 className="text-2xl font-bold">Users</h1>
            <p className="text-muted-foreground">
              Manage users and their group assignments
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/users/create">
              <Plus className="h-4 w-4 mr-2" />
              Add User
            </Link>
          </Button>
        </div>
      }
    >
      <Head title="Users" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardContent className="p-6">
            <DataTable
              columns={columns}
              data={users}
              searchPlaceholder="Search users..."
              searchColumn="name"
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
