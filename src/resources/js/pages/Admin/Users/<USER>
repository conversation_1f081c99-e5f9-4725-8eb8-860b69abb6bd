import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/components/ui/data-table';
import { ArrowLeft, Edit, User, Key, BarChart3, DollarSign, Calendar, CheckCircle, XCircle } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';

interface User {
  id: string;
  name: string;
  email: string;
  email_verified_at: string | null;
  created_at: string;
  groups: Array<{
    id: string;
    name: string;
    description?: string;
  }>;
  api_keys: Array<{
    id: string;
    name: string;
    key_prefix: string;
    is_active: boolean;
    last_used_at: string | null;
    expires_at: string | null;
  }>;
  stats?: {
    total_requests: number;
    total_tokens: number;
    total_cost: number;
    success_rate: number;
    last_request_at: string | null;
  };
}

interface ShowUserProps {
  user: User;
}

export default function Show({ user }: ShowUserProps) {
  const apiKeyColumns: ColumnDef<any>[] = [
    {
      accessorKey: 'name',
      header: 'API Key',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue('name')}</div>
          <div className="text-sm text-muted-foreground font-mono">
            {row.original.key_prefix}...
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('is_active');
        const expiresAt = row.original.expires_at;
        const isExpired = expiresAt && new Date(expiresAt) < new Date();
        
        if (!isActive || isExpired) {
          return <Badge variant="destructive">Inactive</Badge>;
        }
        return <Badge variant="default">Active</Badge>;
      },
    },
    {
      accessorKey: 'last_used_at',
      header: 'Last Used',
      cell: ({ row }) => {
        const lastUsed = row.getValue('last_used_at') as string | null;
        return (
          <div className="text-sm">
            {lastUsed ? (
              new Date(lastUsed).toLocaleDateString()
            ) : (
              <span className="text-muted-foreground">Never</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'expires_at',
      header: 'Expires',
      cell: ({ row }) => {
        const expiresAt = row.getValue('expires_at') as string | null;
        return (
          <div className="text-sm">
            {expiresAt ? (
              new Date(expiresAt).toLocaleDateString()
            ) : (
              <span className="text-muted-foreground">Never</span>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/users">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Link>
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <User className="h-6 w-6 text-muted-foreground" />
              <h1 className="text-2xl font-bold">{user.name}</h1>
              {user.email_verified_at ? (
                <Badge variant="default">Verified</Badge>
              ) : (
                <Badge variant="destructive">Unverified</Badge>
              )}
            </div>
            <p className="text-muted-foreground">
              User details and activity
            </p>
          </div>
          <Button asChild>
            <Link href={`/admin/users/${user.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit User
            </Link>
          </Button>
        </div>
      }
    >
      <Head title={user.name} />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        {/* User Information */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Name</Label>
                <p className="text-sm text-muted-foreground">{user.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Email</Label>
                <p className="text-sm text-muted-foreground">{user.email}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Email Status</Label>
                <div className="flex items-center mt-1">
                  {user.email_verified_at ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-green-600">Verified</span>
                      <span className="text-sm text-muted-foreground ml-2">
                        on {new Date(user.email_verified_at).toLocaleDateString()}
                      </span>
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 text-yellow-600 mr-2" />
                      <span className="text-yellow-600">Unverified</span>
                    </>
                  )}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Member Since</Label>
                <div className="flex items-center mt-1">
                  <Calendar className="h-4 w-4 text-muted-foreground mr-2" />
                  <span className="text-sm text-muted-foreground">
                    {new Date(user.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          {user.stats && (
            <Card>
              <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Total Requests</Label>
                  <div className="flex items-center mt-1">
                    <BarChart3 className="h-4 w-4 text-muted-foreground mr-2" />
                    <span className="text-2xl font-bold">{user.stats.total_requests.toLocaleString()}</span>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Tokens</Label>
                  <p className="text-2xl font-bold">{user.stats.total_tokens.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Cost</Label>
                  <div className="flex items-center mt-1">
                    <DollarSign className="h-4 w-4 text-muted-foreground mr-2" />
                    <span className="text-2xl font-bold">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                      }).format(user.stats.total_cost)}
                    </span>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Success Rate</Label>
                  <p className="text-2xl font-bold">{user.stats.success_rate.toFixed(1)}%</p>
                </div>
                {user.stats.last_request_at && (
                  <div>
                    <Label className="text-sm font-medium">Last Request</Label>
                    <p className="text-sm text-muted-foreground">
                      {new Date(user.stats.last_request_at).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Groups */}
        <Card>
          <CardHeader>
            <CardTitle>Groups ({user.groups.length})</CardTitle>
            <CardDescription>
              Groups this user belongs to
            </CardDescription>
          </CardHeader>
          <CardContent>
            {user.groups.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {user.groups.map((group) => (
                  <div key={group.id} className="flex items-center gap-2">
                    <Badge variant="outline" className="text-sm">
                      {group.name}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No groups assigned</p>
            )}
          </CardContent>
        </Card>

        {/* API Keys */}
        <Card>
          <CardHeader>
            <CardTitle>API Keys ({user.api_keys.length})</CardTitle>
            <CardDescription>
              API keys created by this user
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              columns={apiKeyColumns}
              data={user.api_keys}
              searchPlaceholder="Search API keys..."
              searchColumn="name"
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

function Label({ className, children, ...props }: React.LabelHTMLAttributes<HTMLLabelElement>) {
  return (
    <label className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className}`} {...props}>
      {children}
    </label>
  );
}
