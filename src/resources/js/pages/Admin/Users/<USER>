import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Save, UserPlus } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Group {
  id: string;
  name: string;
  description?: string;
}

interface CreateUserProps {
  groups: Group[];
}

interface FormData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  groups: string[];
}

export default function Create({ groups }: CreateUserProps) {
  const { data, setData, post, processing, errors } = useForm<FormData>({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    groups: [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post('/admin/users');
  };

  const handleGroupToggle = (groupId: string) => {
    const currentGroups = [...data.groups];
    const index = currentGroups.indexOf(groupId);
    
    if (index > -1) {
      currentGroups.splice(index, 1);
    } else {
      currentGroups.push(groupId);
    }
    
    setData('groups', currentGroups);
  };

  return (
    <AdminLayout
      header={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/users">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Create User</h1>
            <p className="text-muted-foreground">
              Add a new user to your system
            </p>
          </div>
        </div>
      }
    >
      <Head title="Create User" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Card>
          <CardHeader>
            <CardTitle>User Details</CardTitle>
            <CardDescription>
              Configure the basic settings for the new user
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    type="text"
                    value={data.name}
                    onChange={(e) => setData('name', e.target.value)}
                    placeholder="Enter full name"
                    required
                  />
                  {errors.name && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.name}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={data.email}
                    onChange={(e) => setData('email', e.target.value)}
                    placeholder="Enter email address"
                    required
                  />
                  {errors.email && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.email}</AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={data.password}
                    onChange={(e) => setData('password', e.target.value)}
                    placeholder="Enter password"
                    required
                  />
                  {errors.password && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.password}</AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password_confirmation">Confirm Password</Label>
                  <Input
                    id="password_confirmation"
                    type="password"
                    value={data.password_confirmation}
                    onChange={(e) => setData('password_confirmation', e.target.value)}
                    placeholder="Confirm password"
                    required
                  />
                  {errors.password_confirmation && (
                    <Alert variant="destructive">
                      <AlertDescription>{errors.password_confirmation}</AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>

              {groups.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Group Assignments</CardTitle>
                    <CardDescription>
                      Select which groups this user should belong to
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-3 md:grid-cols-2">
                      {groups.map((group) => (
                        <div key={group.id} className="flex items-start space-x-2">
                          <Checkbox
                            id={group.id}
                            checked={data.groups.includes(group.id)}
                            onCheckedChange={() => handleGroupToggle(group.id)}
                          />
                          <div className="grid gap-1.5 leading-none">
                            <Label htmlFor={group.id} className="font-medium">
                              {group.name}
                            </Label>
                            {group.description && (
                              <p className="text-xs text-muted-foreground">
                                {group.description}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    {errors.groups && (
                      <Alert variant="destructive" className="mt-4">
                        <AlertDescription>{errors.groups}</AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              )}

              <div className="flex justify-end gap-4">
                <Button variant="outline" asChild>
                  <Link href="/admin/users">Cancel</Link>
                </Button>
                <Button type="submit" disabled={processing}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  {processing ? 'Creating...' : 'Create User'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
