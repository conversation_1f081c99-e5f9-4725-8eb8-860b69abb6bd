import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import UserLayout from '@/layouts/UserLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {
    Key,
    Plus,
    Trash2,
    RefreshCw,
    Copy,
    Eye,
    EyeOff,
    AlertTriangle,
    Users
} from 'lucide-react';

interface ApiKey {
    id: string;
    name: string;
    key_prefix: string;
    is_active: boolean;
    expires_at: string | null;
    last_used_at: string | null;
    created_at: string;
    request_logs_count: number;
    group?: {
        id: string;
        name: string;
        description?: string;
    };
}

interface Props {
    apiKeys: ApiKey[];
    flash?: {
        success?: string;
        api_key_value?: string;
    };
}

export default function ApiKeysIndex({ apiKeys, flash }: Props) {
    const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; apiKey: ApiKey | null }>({
        open: false,
        apiKey: null,
    });
    const [showApiKey, setShowApiKey] = useState(false);

    const breadcrumbItems = [
        { name: 'Dashboard', href: '/dashboard' },
        { name: 'API Keys', current: true },
    ];

    const handleDelete = (apiKey: ApiKey) => {
        setDeleteDialog({ open: true, apiKey });
    };

    const confirmDelete = () => {
        if (deleteDialog.apiKey) {
            router.delete(`/dashboard/api-keys/${deleteDialog.apiKey.id}`, {
                onSuccess: () => {
                    setDeleteDialog({ open: false, apiKey: null });
                },
            });
        }
    };

    const handleRegenerate = (apiKey: ApiKey) => {
        router.post(`/dashboard/api-keys/${apiKey.id}/regenerate`);
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
    };

    return (
        <UserLayout breadcrumbs={breadcrumbItems}>
            <Head title="API Keys" />

            <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">API Keys</h1>
                        <p className="text-muted-foreground">
                            Manage your API keys for accessing the LLM router
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/dashboard/api-keys/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Create API Key
                        </Link>
                    </Button>
                </div>

                {/* Success Message */}
                {flash?.success && (
                    <Alert>
                        <AlertDescription>{flash.success}</AlertDescription>
                    </Alert>
                )}

                {/* New API Key Display */}
                {flash?.api_key_value && (
                    <Alert>
                        <Key className="h-4 w-4" />
                        <AlertDescription>
                            <div className="space-y-2">
                                <p className="font-semibold">Your new API key has been created!</p>
                                <p className="text-sm">
                                    Please copy this key now. You won't be able to see it again.
                                </p>
                                <div className="flex items-center gap-2 p-2 bg-muted rounded font-mono text-sm">
                                    <span className={showApiKey ? '' : 'blur-sm select-none'}>
                                        {flash.api_key_value}
                                    </span>
                                    <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => setShowApiKey(!showApiKey)}
                                    >
                                        {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </Button>
                                    <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => copyToClipboard(flash.api_key_value!)}
                                    >
                                        <Copy className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </AlertDescription>
                    </Alert>
                )}

                {/* API Keys List */}
                {apiKeys.length > 0 ? (
                    <div className="grid gap-4">
                        {apiKeys.map((apiKey) => (
                            <Card key={apiKey.id}>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <Key className="h-5 w-5 text-primary" />
                                            <div>
                                                <CardTitle className="text-lg">{apiKey.name}</CardTitle>
                                                <CardDescription className="font-mono">
                                                    {apiKey.key_prefix}
                                                </CardDescription>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge variant={apiKey.is_active ? "default" : "secondary"}>
                                                {apiKey.is_active ? "Active" : "Inactive"}
                                            </Badge>
                                            {apiKey.group && (
                                                <Badge variant="outline" className="flex items-center gap-1">
                                                    <Users className="h-3 w-3" />
                                                    {apiKey.group.name}
                                                </Badge>
                                            )}
                                            {apiKey.expires_at && (
                                                <Badge variant="outline">
                                                    Expires: {new Date(apiKey.expires_at).toLocaleDateString()}
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center justify-between">
                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <p className="text-muted-foreground">Created</p>
                                                <p>{new Date(apiKey.created_at).toLocaleDateString()}</p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Last Used</p>
                                                <p>
                                                    {apiKey.last_used_at
                                                        ? new Date(apiKey.last_used_at).toLocaleDateString()
                                                        : 'Never'
                                                    }
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Total Requests</p>
                                                <p>{apiKey.request_logs_count.toLocaleString()}</p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Group Assignment</p>
                                                <p>{apiKey.group ? apiKey.group.name : 'All groups'}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleRegenerate(apiKey)}
                                            >
                                                <RefreshCw className="mr-2 h-4 w-4" />
                                                Regenerate
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="destructive"
                                                onClick={() => handleDelete(apiKey)}
                                            >
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Key className="mx-auto h-12 w-12 text-muted-foreground" />
                            <h3 className="mt-4 text-lg font-semibold">No API keys</h3>
                            <p className="mt-2 text-muted-foreground">
                                Get started by creating your first API key.
                            </p>
                            <div className="mt-6">
                                <Button asChild>
                                    <Link href="/dashboard/api-keys/create">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Create API Key
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Delete Confirmation Dialog */}
                <Dialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({ open, apiKey: null })}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-5 w-5 text-destructive" />
                                Delete API Key
                            </DialogTitle>
                            <DialogDescription>
                                Are you sure you want to delete the API key "{deleteDialog.apiKey?.name}"? 
                                This action cannot be undone and will immediately revoke access for any applications using this key.
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <Button 
                                variant="outline" 
                                onClick={() => setDeleteDialog({ open: false, apiKey: null })}
                            >
                                Cancel
                            </Button>
                            <Button variant="destructive" onClick={confirmDelete}>
                                Delete API Key
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </UserLayout>
    );
}
