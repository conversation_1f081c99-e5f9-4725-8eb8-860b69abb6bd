import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import UserLayout from '@/layouts/UserLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    Key,
    Activity,
    Plus,
    ExternalLink,
    Settings
} from 'lucide-react';

interface User {
    id: string;
    name: string;
    email: string;
    email_verified_at: string | null;
    groups: Array<{
        id: string;
        name: string;
        description?: string;
        llm_models: Array<{
            id: string;
            name: string;
            model_identifier: string;
        }>;
    }>;
    api_keys: Array<{
        id: string;
        name: string;
        key_prefix: string;
        is_active: boolean;
        expires_at: string | null;
        last_used_at: string | null;
        created_at: string;
    }>;
}

interface Stats {
    total_api_keys: number;
    active_api_keys: number;
    total_groups: number;
    accessible_models: number;
}

interface Props {
    user: User;
    stats: Stats;
    recentUsage: any[];
}

export default function Dashboard({ user, stats, recentUsage }: Props) {
    const breadcrumbItems = [
        { name: 'Dashboard', current: true },
    ];

    return (
        <UserLayout breadcrumbs={breadcrumbItems}>
            <Head title="Dashboard" />

            <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                {/* Welcome Section */}
                <div className="grid auto-rows-min gap-4 md:grid-cols-3">
                    <div className="aspect-video rounded-xl bg-muted/50 p-6 flex flex-col justify-center">
                        <h1 className="text-2xl font-bold">Welcome back, {user.name}!</h1>
                        <p className="text-muted-foreground mt-2">
                            Manage your API keys and view your account information.
                        </p>
                    </div>
                    <div className="aspect-video rounded-xl bg-muted/50 p-6 flex flex-col justify-center">
                        <div className="flex items-center gap-2">
                            <Key className="h-5 w-5 text-primary" />
                            <span className="text-2xl font-bold">{stats.active_api_keys}</span>
                        </div>
                        <p className="text-muted-foreground">Active API Keys</p>
                    </div>
                    <Link href="/dashboard/models" className="aspect-video rounded-xl bg-muted/50 p-6 flex flex-col justify-center hover:bg-muted/70 transition-colors cursor-pointer">
                        <div className="flex items-center gap-2">
                            <Activity className="h-5 w-5 text-primary" />
                            <span className="text-2xl font-bold">{stats.accessible_models}</span>
                        </div>
                        <p className="text-muted-foreground">Accessible Models</p>
                    </Link>
                </div>

                {/* Quick Actions */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2">
                                <Key className="h-4 w-4" />
                                API Keys
                            </CardTitle>
                            <CardDescription>
                                Manage your API keys for accessing the LLM router
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center justify-between">
                                <span className="text-2xl font-bold">{stats.total_api_keys}</span>
                                <Button asChild size="sm">
                                    <Link href="/dashboard/api-keys">
                                        View All
                                        <ExternalLink className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2">
                                <Settings className="h-4 w-4" />
                                Account
                            </CardTitle>
                            <CardDescription>
                                Manage your profile and account settings
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button asChild size="sm" variant="outline" className="w-full">
                                <Link href="/settings/profile">
                                    Settings
                                    <ExternalLink className="ml-2 h-4 w-4" />
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Quick Create</CardTitle>
                            <CardDescription>
                                Create a new API key quickly
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button asChild size="sm" className="w-full">
                                <Link href="/dashboard/api-keys/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    New API Key
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>


            </div>
        </UserLayout>
    );
}
