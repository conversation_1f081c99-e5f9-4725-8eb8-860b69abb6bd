import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import UserLayout from '@/layouts/UserLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Activity, 
    Users, 
    ArrowLeft,
    Server,
    Cpu
} from 'lucide-react';

interface Provider {
    id: string;
    name: string;
    type: string;
}

interface LlmModel {
    id: string;
    name: string;
    model_identifier: string;
    provider: Provider;
    is_active: boolean;
}

interface Group {
    id: string;
    name: string;
    description?: string;
    llm_models: LlmModel[];
}

interface User {
    id: string;
    name: string;
    email: string;
    groups: Group[];
}

interface Props {
    user: User;
    accessibleModels: LlmModel[];
}

export default function Models({ user, accessibleModels }: Props) {
    const breadcrumbItems = [
        { name: 'Dashboard', href: '/dashboard' },
        { name: 'Accessible Models', current: true },
    ];

    // Group models by provider for better organization
    const modelsByProvider = accessibleModels.reduce((acc, model) => {
        const providerName = model.provider.name;
        if (!acc[providerName]) {
            acc[providerName] = [];
        }
        acc[providerName].push(model);
        return acc;
    }, {} as Record<string, LlmModel[]>);

    return (
        <UserLayout breadcrumbs={breadcrumbItems}>
            <Head title="Accessible Models" />

            <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Accessible Models</h1>
                        <p className="text-muted-foreground">
                            Models you have access to through your group memberships
                        </p>
                    </div>
                    <Button asChild variant="outline">
                        <Link href="/dashboard">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Dashboard
                        </Link>
                    </Button>
                </div>

                {/* Summary Stats */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2">
                                <Activity className="h-4 w-4" />
                                Total Models
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <span className="text-2xl font-bold">{accessibleModels.length}</span>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2">
                                <Server className="h-4 w-4" />
                                Providers
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <span className="text-2xl font-bold">{Object.keys(modelsByProvider).length}</span>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                Groups
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <span className="text-2xl font-bold">{user.groups.length}</span>
                        </CardContent>
                    </Card>
                </div>

                {/* Models by Provider */}
                {Object.keys(modelsByProvider).length > 0 ? (
                    <div className="space-y-6">
                        {Object.entries(modelsByProvider).map(([providerName, models]) => (
                            <Card key={providerName}>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Server className="h-5 w-5" />
                                        {providerName}
                                    </CardTitle>
                                    <CardDescription>
                                        {models.length} model{models.length !== 1 ? 's' : ''} available
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                                        {models.map((model) => (
                                            <div key={model.id} className="p-3 border rounded-lg">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <Cpu className="h-4 w-4 text-primary" />
                                                    <h3 className="font-semibold">{model.name}</h3>
                                                </div>
                                                <p className="text-sm text-muted-foreground mb-2">
                                                    {model.model_identifier}
                                                </p>
                                                <Badge variant="outline" className="text-xs">
                                                    {model.provider.type}
                                                </Badge>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Activity className="mx-auto h-12 w-12 text-muted-foreground" />
                            <h3 className="mt-4 text-lg font-semibold">No models available</h3>
                            <p className="mt-2 text-muted-foreground">
                                You don't have access to any models yet. Contact your administrator to be added to groups with model access.
                            </p>
                        </CardContent>
                    </Card>
                )}

                {/* Groups Overview */}
                {user.groups.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Your Groups</CardTitle>
                            <CardDescription>
                                Groups that provide you access to models
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2">
                                {user.groups.map((group) => (
                                    <div key={group.id} className="p-4 border rounded-lg">
                                        <div className="flex items-center gap-2 mb-2">
                                            <Users className="h-4 w-4 text-primary" />
                                            <h3 className="font-semibold">{group.name}</h3>
                                        </div>
                                        {group.description && (
                                            <p className="text-sm text-muted-foreground mb-3">{group.description}</p>
                                        )}
                                        <div className="flex flex-wrap gap-1">
                                            {group.llm_models.slice(0, 3).map((model) => (
                                                <Badge key={model.id} variant="outline" className="text-xs">
                                                    {model.name}
                                                </Badge>
                                            ))}
                                            {group.llm_models.length > 3 && (
                                                <Badge variant="outline" className="text-xs">
                                                    +{group.llm_models.length - 3} more
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </UserLayout>
    );
}
