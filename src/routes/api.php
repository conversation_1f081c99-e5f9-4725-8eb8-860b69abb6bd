<?php

use App\Http\Controllers\Api\LlmApiController;
use App\Http\Middleware\ApiKeyAuthMiddleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// OpenAI-compatible API endpoints
Route::prefix('v1')->middleware([ApiKeyAuthMiddleware::class])->group(function () {
    // Chat completions endpoint
    Route::post('/chat/completions', [LlmApiController::class, 'chatCompletions']);

    // Text completions endpoint
    Route::post('/completions', [LlmApiController::class, 'completions']);

    // Responses endpoint (newer OpenAI API)
    Route::post('/responses', [LlmApiController::class, 'responses']);

    // Models endpoint
    Route::get('/models', [LlmApiController::class, 'models']);
});

// Legacy Sanctum route for testing
Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');
