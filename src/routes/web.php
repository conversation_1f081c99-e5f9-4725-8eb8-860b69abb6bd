<?php

use App\Http\Controllers\Admin\ProviderController;
use App\Http\Controllers\Admin\LlmModelController;
use App\Http\Controllers\Admin\GroupController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\ApiKeyController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\User\DashboardController;
use App\Http\Controllers\User\ApiKeyController as UserApiKeyController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    // User Dashboard Routes
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard/models', [DashboardController::class, 'models'])->name('dashboard.models');

    // User API Key Management
    Route::prefix('dashboard')->name('dashboard.')->group(function () {
        Route::resource('api-keys', UserApiKeyController::class)->only(['index', 'create', 'store', 'destroy']);
        Route::post('api-keys/{apiKey}/regenerate', [UserApiKeyController::class, 'regenerate'])->name('api-keys.regenerate');
    });

    // Admin routes - protected by admin middleware
    Route::prefix('admin')->name('admin.')->middleware('admin')->group(function () {
        // Analytics Dashboard
        Route::get('/', [AnalyticsController::class, 'index'])->name('dashboard');
        Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
        Route::get('/analytics/export', [AnalyticsController::class, 'export'])->name('analytics.export');

        // Provider Management
        Route::resource('providers', ProviderController::class);
        Route::post('providers/{provider}/test', [ProviderController::class, 'testConnection'])->name('providers.test');

        // Model Management
        Route::resource('models', LlmModelController::class);

        // Group Management
        Route::resource('groups', GroupController::class);

        // User Management
        Route::resource('users', UserController::class);
        Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

        // API Key Management (Admin - all users)
        Route::resource('api-keys', ApiKeyController::class);
        Route::post('api-keys/{apiKey}/revoke', [ApiKeyController::class, 'revoke'])->name('api-keys.revoke');
        Route::post('api-keys/{apiKey}/activate', [ApiKeyController::class, 'activate'])->name('api-keys.activate');
        Route::get('api-keys/{apiKey}/usage', [ApiKeyController::class, 'usage'])->name('api-keys.usage');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
