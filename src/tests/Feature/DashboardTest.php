<?php

use App\Models\User;
use App\Models\Group;

test('guests are redirected to the login page', function () {
    $this->get('/dashboard')->assertRedirect('/login');
});

test('authenticated users can visit the user dashboard', function () {
    $this->actingAs(User::factory()->create());

    $this->get('/dashboard')->assertOk();
});

test('guests cannot access admin dashboard', function () {
    $this->get('/admin')->assertRedirect('/login');
});

test('regular users cannot access admin dashboard', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $this->get('/admin')->assertStatus(403);
});

test('admin users can access admin dashboard', function () {
    // Create admin group
    $adminGroup = Group::create([
        'name' => 'Administrators',
        'description' => 'Admin group for testing',
        'is_active' => true,
        'permissions' => ['admin_access' => true],
    ]);

    $user = User::factory()->create();

    // Use the UserGroup model to create the relationship
    \App\Models\UserGroup::create([
        'user_id' => $user->id,
        'group_id' => $adminGroup->id,
    ]);

    $this->actingAs($user);

    $this->get('/admin')->assertOk();
});

test('admin users can access admin users page', function () {
    // Create admin group
    $adminGroup = Group::create([
        'name' => 'Administrators',
        'description' => 'Admin group for testing',
        'is_active' => true,
        'permissions' => ['admin_access' => true],
    ]);

    $user = User::factory()->create();

    // Use the UserGroup model to create the relationship
    \App\Models\UserGroup::create([
        'user_id' => $user->id,
        'group_id' => $adminGroup->id,
    ]);

    $this->actingAs($user);

    $this->get('/admin/users')->assertOk();
});